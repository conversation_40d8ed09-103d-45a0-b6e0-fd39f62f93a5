#!/usr/bin/env python3
"""
Script de test pour les nouvelles API des coûts améliorés
"""

import requests
import json

def test_improved_costs_api():
    """Tester les nouvelles API des coûts améliorés"""
    base_url = "http://localhost:5000"
    
    print("🧪 Test des API des coûts logistiques améliorés...")
    
    try:
        # Test de l'endpoint des coûts améliorés
        print("📊 Test de l'endpoint /api/costs-improved...")
        response = requests.get(f"{base_url}/api/costs-improved", timeout=5, allow_redirects=False)
        if response.status_code in [200, 302, 401]:
            print("✅ Endpoint /api/costs-improved accessible")
        else:
            print(f"❌ Erreur endpoint costs-improved: {response.status_code}")
        
        # Test de création d'un coût amélioré (sans authentification, donc 302 attendu)
        print("📝 Test de création de coût amélioré...")
        test_data = {
            "order_id": 1,
            "exchange_rate": 135.50000,
            "currency": "USD",
            "shipment_type": "SEA",
            "fob_amount": 1000.00,
            "freight": 200.00,
            "customs_duties": {
                "d3_number": "D3-2024-001",
                "d3_date": "2024-01-15",
                "quittance1_ttc": 15000.00,
                "quittance1_tva": 2500.00
            }
        }
        
        response = requests.post(
            f"{base_url}/api/costs-improved",
            json=test_data,
            timeout=5,
            allow_redirects=False
        )
        
        if response.status_code in [201, 302, 401]:
            print("✅ Endpoint POST /api/costs-improved fonctionne")
        else:
            print(f"❌ Erreur POST costs-improved: {response.status_code}")
        
        # Vérifier la structure de la base de données
        print("🗄️  Vérification de la structure de la base de données...")
        import sqlite3
        
        conn = sqlite3.connect('logistics.db')
        cursor = conn.cursor()
        
        # Vérifier les nouvelles tables
        tables_to_check = [
            'logistic_costs_main',
            'customs_duties_improved',
            'port_fees_improved',
            'shipping_company_fees_improved',
            'other_expenses_improved',
            'logistic_followups_improved'
        ]
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ Table {table}: {count} enregistrements")
            except sqlite3.Error as e:
                print(f"❌ Erreur table {table}: {e}")
        
        # Vérifier les triggers
        cursor.execute("SELECT name FROM sqlite_master WHERE type='trigger'")
        triggers = cursor.fetchall()
        print(f"✅ Triggers actifs: {len(triggers)}")
        
        conn.close()
        
        print("\n🎉 Tests des coûts améliorés terminés!")
        print("📋 Fonctionnalités disponibles:")
        print("   - Tables améliorées avec calculs automatiques")
        print("   - API REST pour les coûts logistiques")
        print("   - Triggers pour maintenir la cohérence")
        print("   - Export Excel multi-feuilles")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter à l'application")
        return False
    
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

if __name__ == "__main__":
    success = test_improved_costs_api()
    if success:
        print("\n✨ Tous les tests sont passés avec succès!")
    else:
        print("\n❌ Certains tests ont échoué.")
