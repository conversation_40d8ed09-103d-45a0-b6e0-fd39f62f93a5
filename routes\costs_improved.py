import sqlite3
from datetime import datetime
from io import BytesIO

import pandas as pd
from flask import Blueprint, request, jsonify, g, send_file

costs_improved_bp = Blueprint('costs_improved', __name__)

def get_db():
    """Obtenir une connexion à la base de données"""
    db = sqlite3.connect('logistics.db')
    db.row_factory = sqlite3.Row
    return db

@costs_improved_bp.route('/costs-improved', methods=['GET'])
def get_logistic_costs_improved():
    """Récupérer tous les coûts logistiques améliorés"""
    try:
        db = get_db()
        cursor = db.cursor()
        
        cursor.execute("""
            SELECT 
                lcm.*,
                oh.order_number,
                s.name as supplier_name
            FROM logistic_costs_main lcm
            JOIN order_headers oh ON lcm.order_header_id = oh.id
            JOIN suppliers s ON oh.supplier_id = s.id
            ORDER BY lcm.created_at DESC
        """)
        
        costs = [dict(row) for row in cursor.fetchall()]
        db.close()
        
        return jsonify({'costs': costs})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@costs_improved_bp.route('/costs-improved', methods=['POST'])
def create_logistic_cost_improved():
    """Créer un nouveau coût logistique avec calculs automatiques"""
    data = request.get_json()
    
    if not data or 'order_id' not in data:
        return jsonify({'error': 'ID de commande requis'}), 400
    
    try:
        db = get_db()
        cursor = db.cursor()
        
        # 1. Créer le coût principal
        cursor.execute("""
            INSERT INTO logistic_costs_main (
                order_header_id, exchange_rate, currency, shipment_type,
                goods_price_currency, fob_amount, freight, total_amount_cif
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            data['order_id'],
            data.get('exchange_rate', 135.50000),
            data.get('currency', 'USD'),
            data.get('shipment_type', 'SEA'),
            data.get('goods_price_currency'),
            data.get('fob_amount'),
            data.get('freight'),
            (data.get('fob_amount', 0) + data.get('freight', 0))
        ))
        
        cost_id = cursor.lastrowid
        
        # 2. Ajouter les droits de douane si fournis
        if 'customs_duties' in data:
            customs = data['customs_duties']
            cursor.execute("""
                INSERT INTO customs_duties_improved (
                    logistic_cost_id, d3_number, d3_date,
                    quittance1_number, quittance1_date, quittance1_ttc, quittance1_tva,
                    quittance2_number, quittance2_date, quittance2_ttc, quittance2_tva,
                    notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                cost_id,
                customs.get('d3_number'),
                customs.get('d3_date'),
                customs.get('quittance1_number'),
                customs.get('quittance1_date'),
                customs.get('quittance1_ttc'),
                customs.get('quittance1_tva'),
                customs.get('quittance2_number'),
                customs.get('quittance2_date'),
                customs.get('quittance2_ttc'),
                customs.get('quittance2_tva'),
                customs.get('notes')
            ))
        
        # 3. Ajouter les frais portuaires si fournis
        if 'port_fees' in data:
            port = data['port_fees']
            cursor.execute("""
                INSERT INTO port_fees_improved (
                    logistic_cost_id,
                    import_delivery_invoice, import_delivery_date, import_delivery_ttc, import_delivery_tva,
                    customs_inspection_invoice, customs_inspection_date, customs_inspection_ttc, customs_inspection_tva,
                    demurrage_invoice, demurrage_date, demurrage_ttc, demurrage_tva,
                    notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                cost_id,
                port.get('import_delivery_invoice'), port.get('import_delivery_date'),
                port.get('import_delivery_ttc'), port.get('import_delivery_tva'),
                port.get('customs_inspection_invoice'), port.get('customs_inspection_date'),
                port.get('customs_inspection_ttc'), port.get('customs_inspection_tva'),
                port.get('demurrage_invoice'), port.get('demurrage_date'),
                port.get('demurrage_ttc'), port.get('demurrage_tva'),
                port.get('notes')
            ))
        
        # 4. Ajouter les frais de transporteur si fournis
        if 'shipping_fees' in data:
            shipping = data['shipping_fees']
            cursor.execute("""
                INSERT INTO shipping_company_fees_improved (
                    logistic_cost_id,
                    shipping_agency_invoice, shipping_agency_date, shipping_agency_ttc, shipping_agency_tva,
                    empty_containers_invoice, empty_containers_date, empty_containers_ttc, empty_containers_tva,
                    demurrage_invoice, demurrage_date, demurrage_ht,
                    notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                cost_id,
                shipping.get('shipping_agency_invoice'), shipping.get('shipping_agency_date'),
                shipping.get('shipping_agency_ttc'), shipping.get('shipping_agency_tva'),
                shipping.get('empty_containers_invoice'), shipping.get('empty_containers_date'),
                shipping.get('empty_containers_ttc'), shipping.get('empty_containers_tva'),
                shipping.get('demurrage_invoice'), shipping.get('demurrage_date'),
                shipping.get('demurrage_ht'),
                shipping.get('notes')
            ))
        
        # 5. Ajouter les autres frais si fournis
        if 'other_expenses' in data:
            for expense in data['other_expenses']:
                cursor.execute("""
                    INSERT INTO other_expenses_improved (
                        logistic_cost_id, expense_type, invoice_number,
                        invoice_date, total_ttc, tva, description
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    cost_id,
                    expense.get('expense_type'),
                    expense.get('invoice_number'),
                    expense.get('invoice_date'),
                    expense.get('total_ttc'),
                    expense.get('tva'),
                    expense.get('description')
                ))
        
        # 6. Calculer le Landed Cost total
        cursor.execute("""
            SELECT 
                COALESCE((SELECT total_ht FROM customs_duties_improved WHERE logistic_cost_id = ?), 0) +
                COALESCE((SELECT total_ht FROM port_fees_improved WHERE logistic_cost_id = ?), 0) +
                COALESCE((SELECT total_ht FROM shipping_company_fees_improved WHERE logistic_cost_id = ?), 0) +
                COALESCE((SELECT SUM(ht) FROM other_expenses_improved WHERE logistic_cost_id = ?), 0) as total_logistic_ht
        """, (cost_id, cost_id, cost_id, cost_id))
        
        total_logistic_ht = cursor.fetchone()[0] or 0
        
        # Récupérer le CIF DZD
        cursor.execute("SELECT total_amount_cif_dzd FROM logistic_costs_main WHERE id = ?", (cost_id,))
        cif_dzd = cursor.fetchone()[0] or 0
        
        # Calculer le Landed Cost total et le coefficient
        landed_cost_ht = cif_dzd + total_logistic_ht
        coefficient = landed_cost_ht / cif_dzd if cif_dzd > 0 else 0
        
        # Mettre à jour le coût principal
        cursor.execute("""
            UPDATE logistic_costs_main 
            SET landed_cost_ht = ?, landed_cost_coefficient = ?
            WHERE id = ?
        """, (landed_cost_ht, coefficient, cost_id))
        
        db.commit()
        db.close()
        
        return jsonify({
            'message': 'Coût logistique créé avec calculs automatiques',
            'cost_id': cost_id,
            'landed_cost_ht': landed_cost_ht,
            'landed_cost_coefficient': coefficient,
            'total_logistic_costs': total_logistic_ht
        }), 201
    
    except Exception as e:
        if db:
            db.rollback()
            db.close()
        return jsonify({'error': str(e)}), 500

@costs_improved_bp.route('/costs-improved/<int:cost_id>/details', methods=['GET'])
def get_cost_details_improved(cost_id):
    """Récupérer tous les détails d'un coût logistique amélioré"""
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Récupérer le coût principal
        cursor.execute("""
            SELECT lcm.*, oh.order_number, s.name as supplier_name
            FROM logistic_costs_main lcm
            JOIN order_headers oh ON lcm.order_header_id = oh.id
            JOIN suppliers s ON oh.supplier_id = s.id
            WHERE lcm.id = ?
        """, (cost_id,))
        
        main_cost = cursor.fetchone()
        if not main_cost:
            return jsonify({'error': 'Coût logistique non trouvé'}), 404
        
        # Récupérer les droits de douane
        cursor.execute("SELECT * FROM customs_duties_improved WHERE logistic_cost_id = ?", (cost_id,))
        customs_duties = [dict(row) for row in cursor.fetchall()]
        
        # Récupérer les frais portuaires
        cursor.execute("SELECT * FROM port_fees_improved WHERE logistic_cost_id = ?", (cost_id,))
        port_fees = [dict(row) for row in cursor.fetchall()]
        
        # Récupérer les frais de transporteur
        cursor.execute("SELECT * FROM shipping_company_fees_improved WHERE logistic_cost_id = ?", (cost_id,))
        shipping_fees = [dict(row) for row in cursor.fetchall()]
        
        # Récupérer les autres frais
        cursor.execute("SELECT * FROM other_expenses_improved WHERE logistic_cost_id = ?", (cost_id,))
        other_expenses = [dict(row) for row in cursor.fetchall()]
        
        db.close()
        
        return jsonify({
            'main_cost': dict(main_cost),
            'customs_duties': customs_duties,
            'port_fees': port_fees,
            'shipping_fees': shipping_fees,
            'other_expenses': other_expenses
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@costs_improved_bp.route('/costs-improved/<int:cost_id>/export', methods=['GET'])
def export_cost_improved(cost_id):
    """Exporter un coût logistique amélioré au format Excel"""
    try:
        # Récupérer les détails
        response_data = get_cost_details_improved(cost_id)
        if response_data[1] != 200:  # Si erreur
            return response_data
        
        data = response_data[0].get_json()
        
        # Créer le fichier Excel multi-feuilles
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # Feuille principale
            pd.DataFrame([data['main_cost']]).to_excel(writer, sheet_name='Coût Principal', index=False)
            
            # Feuilles détaillées
            if data['customs_duties']:
                pd.DataFrame(data['customs_duties']).to_excel(writer, sheet_name='Droits de Douane', index=False)
            
            if data['port_fees']:
                pd.DataFrame(data['port_fees']).to_excel(writer, sheet_name='Frais Portuaires', index=False)
            
            if data['shipping_fees']:
                pd.DataFrame(data['shipping_fees']).to_excel(writer, sheet_name='Frais Transport', index=False)
            
            if data['other_expenses']:
                pd.DataFrame(data['other_expenses']).to_excel(writer, sheet_name='Autres Frais', index=False)
        
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f"couts_logistiques_{data['main_cost']['order_number']}.xlsx"
        )
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
