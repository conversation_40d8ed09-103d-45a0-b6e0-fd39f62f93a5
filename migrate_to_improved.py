#!/usr/bin/env python3
"""
Script de migration pour appliquer les tables améliorées
"""

import sqlite3
import os
from datetime import datetime

def backup_database():
    """Créer une sauvegarde de la base de données actuelle"""
    if os.path.exists('logistics.db'):
        backup_name = f'logistics_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        os.system(f'copy logistics.db {backup_name}')
        print(f"✅ Sauvegarde créée: {backup_name}")
        return backup_name
    return None

def apply_improved_schema():
    """Appliquer le schéma amélioré"""
    try:
        conn = sqlite3.connect('logistics.db')
        
        # Lire et exécuter le schéma amélioré
        with open('schema_improved.sql', 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        
        # Exécuter le schéma par blocs pour éviter les erreurs
        statements = schema_sql.split(';')
        
        for statement in statements:
            statement = statement.strip()
            if statement:
                try:
                    conn.execute(statement)
                    print(f"✅ Exécuté: {statement[:50]}...")
                except sqlite3.Error as e:
                    print(f"⚠️  Erreur (ignorée): {e}")
                    print(f"   Statement: {statement[:100]}...")
        
        conn.commit()
        conn.close()
        
        print("✅ Schéma amélioré appliqué avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'application du schéma: {e}")
        return False

def migrate_existing_data():
    """Migrer les données existantes vers les nouvelles tables"""
    try:
        conn = sqlite3.connect('logistics.db')
        cursor = conn.cursor()
        
        # Migrer les données de logistic_costs vers logistic_costs_main
        cursor.execute("""
            INSERT OR IGNORE INTO logistic_costs_main (
                order_header_id, exchange_rate, currency, shipment_type,
                goods_price_currency, fob_amount, freight, total_amount_cif,
                fob_amount_dzd, freight_dzd, total_amount_cif_dzd,
                landed_cost_ht, landed_cost_coefficient, total_paid_ttc
            )
            SELECT 
                order_header_id, exchange_rate, currency, shipment_type,
                goods_price_currency, fob_amount, freight, total_amount_cif,
                fob_amount_dzd, freight_dzd, total_amount_cif_dzd,
                landed_cost_ht, landed_cost_coefficient, total_paid_ttc
            FROM logistic_costs
            WHERE EXISTS (SELECT 1 FROM logistic_costs)
        """)
        
        # Migrer les données de logistic_followups vers logistic_followups_improved
        cursor.execute("""
            INSERT OR IGNORE INTO logistic_followups_improved (
                order_header_id, voyage_number, call_at_port, bill_of_lading,
                vessel_name, shipowner, actual_time_of_arrival, status, notes
            )
            SELECT 
                order_header_id, voyage_number, call_at_port, bill_of_lading,
                vessel_name, shipowner, actual_time_of_arrival, status, notes
            FROM logistic_followups
            WHERE EXISTS (SELECT 1 FROM logistic_followups)
        """)
        
        conn.commit()
        conn.close()
        
        print("✅ Migration des données existantes terminée!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la migration: {e}")
        return False

def verify_migration():
    """Vérifier que la migration s'est bien passée"""
    try:
        conn = sqlite3.connect('logistics.db')
        cursor = conn.cursor()
        
        # Vérifier les nouvelles tables
        tables_to_check = [
            'logistic_costs_main',
            'customs_duties_improved',
            'port_fees_improved',
            'shipping_company_fees_improved',
            'other_expenses_improved',
            'logistic_followups_improved'
        ]
        
        for table in tables_to_check:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"✅ Table {table}: {count} enregistrements")
        
        # Vérifier les triggers
        cursor.execute("SELECT name FROM sqlite_master WHERE type='trigger'")
        triggers = cursor.fetchall()
        print(f"✅ Triggers créés: {len(triggers)}")
        for trigger in triggers:
            print(f"   - {trigger[0]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def main():
    """Fonction principale de migration"""
    print("🚀 Début de la migration vers le schéma amélioré...")
    print("=" * 60)
    
    # 1. Sauvegarde
    backup_file = backup_database()
    
    # 2. Application du nouveau schéma
    if not apply_improved_schema():
        print("❌ Échec de l'application du schéma")
        return False
    
    # 3. Migration des données
    if not migrate_existing_data():
        print("❌ Échec de la migration des données")
        return False
    
    # 4. Vérification
    if not verify_migration():
        print("❌ Échec de la vérification")
        return False
    
    print("=" * 60)
    print("🎉 Migration terminée avec succès!")
    print(f"📁 Sauvegarde disponible: {backup_file}")
    print("🔧 Nouvelles fonctionnalités disponibles:")
    print("   - Calculs automatiques des montants HT/TTC/TVA")
    print("   - Gestion granulaire des coûts logistiques")
    print("   - Triggers pour maintenir la cohérence")
    print("   - Suivi logistique amélioré")
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n❌ Migration échouée. Vérifiez les erreurs ci-dessus.")
        print("💡 Vous pouvez restaurer la sauvegarde si nécessaire.")
    else:
        print("\n✨ L'application peut maintenant utiliser les nouvelles fonctionnalités!")
