<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Système de Gestion Logistique{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            margin-left: 0;
        }
        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }
        .card-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .card-stats-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .card-stats-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .card-stats-info {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar position-fixed d-none d-md-block">
        <div class="p-3">
            <h4 class="text-white">Logistics</h4>
            <hr class="text-white">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('dashboard') }}">
                        <i class="fas fa-tachometer-alt me-2"></i>Tableau de Bord
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="loadPage('orders')">
                        <i class="fas fa-shopping-cart me-2"></i>Commandes
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="loadPage('logistics')">
                        <i class="fas fa-ship me-2"></i>Suivi Logistique
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="loadPage('costs')">
                        <i class="fas fa-calculator me-2"></i>Coûts Logistiques
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="loadPage('receipts')">
                        <i class="fas fa-inbox me-2"></i>Réceptions
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="loadPage('reports')">
                        <i class="fas fa-chart-bar me-2"></i>Rapports & KPI
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="loadPage('suppliers')">
                        <i class="fas fa-truck me-2"></i>Fournisseurs
                    </a>
                </li>
            </ul>
            <hr class="text-white">
            <div class="dropdown">
                <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" 
                   id="dropdownUser1" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i>
                    <strong>{{ g.user.username if g.user else 'Utilisateur' }}</strong>
                </a>
                <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
                    <li><a class="dropdown-item" href="#">Profil</a></li>
                    <li><a class="dropdown-item" href="#">Paramètres</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{{ url_for('logout') }}">Déconnexion</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
            <div class="container-fluid">
                <button class="btn btn-outline-secondary d-md-none" type="button" data-bs-toggle="offcanvas" 
                        data-bs-target="#sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="navbar-brand mb-0 h1">{% block page_title %}Tableau de Bord{% endblock %}</span>
                <div class="navbar-nav ms-auto">
                    <div class="nav-item">
                        <span class="nav-link">
                            <i class="fas fa-calendar me-1"></i>
                            <span id="current-date"></span>
                        </span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="container-fluid p-4">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Chargement en cours...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Afficher la date actuelle
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('fr-FR');
        
        // Fonction pour charger les pages dynamiquement
        function loadPage(page) {
            showLoading();
            
            // Simuler le chargement de page
            setTimeout(() => {
                hideLoading();
                // Ici vous pouvez implémenter la logique de chargement AJAX
                console.log('Chargement de la page:', page);
            }, 500);
        }
        
        function showLoading() {
            const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
            modal.show();
        }
        
        function hideLoading() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
            if (modal) {
                modal.hide();
            }
        }
        
        // Fonction utilitaire pour les requêtes AJAX
        async function apiRequest(url, options = {}) {
            showLoading();
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('Erreur API:', error);
                alert('Erreur lors de la requête: ' + error.message);
                throw error;
            } finally {
                hideLoading();
            }
        }
        
        // Fonction pour formater les nombres
        function formatCurrency(amount, currency = 'DZD') {
            return new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: currency
            }).format(amount);
        }
        
        // Fonction pour formater les dates
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('fr-FR');
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
