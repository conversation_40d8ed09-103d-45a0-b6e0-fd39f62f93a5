from flask import Blueprint, request, jsonify, send_file
import sqlite3
import pandas as pd
from io import BytesIO
from datetime import datetime, timedelta
import calendar

reports_bp = Blueprint('reports', __name__)

def get_db():
    """Obtenir une connexion à la base de données"""
    db = sqlite3.connect('logistics.db')
    db.row_factory = sqlite3.Row
    return db

@reports_bp.route('/kpi/dashboard', methods=['GET'])
def get_dashboard_kpi():
    """Récupérer les KPI pour le tableau de bord"""
    try:
        db = get_db()
        cursor = db.cursor()
        
        # KPI généraux
        cursor.execute("""
            SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 'DELIVERED' THEN 1 END) as delivered_orders,
                COUNT(CASE WHEN status = 'SHIPPED' THEN 1 END) as shipped_orders,
                COUNT(CASE WHEN status = 'DRAFT' THEN 1 END) as draft_orders
            FROM order_headers
            WHERE created_at >= date('now', '-30 days')
        """)
        orders_kpi = dict(cursor.fetchone())
        
        # Coûts logistiques du mois
        cursor.execute("""
            SELECT 
                SUM(total_landed_cost_dzd) as total_landed_cost,
                COUNT(*) as total_receipts,
                AVG(total_landed_cost_dzd) as avg_landed_cost
            FROM receipts
            WHERE created_at >= date('now', 'start of month')
        """)
        costs_kpi = dict(cursor.fetchone())
        
        # Top 5 fournisseurs par volume
        cursor.execute("""
            SELECT 
                s.name as supplier_name,
                COUNT(oh.id) as order_count,
                SUM(COALESCE(r.total_landed_cost_dzd, 0)) as total_value
            FROM suppliers s
            LEFT JOIN order_headers oh ON s.id = oh.supplier_id
            LEFT JOIN receipts r ON oh.id = r.order_header_id
            WHERE oh.created_at >= date('now', '-30 days')
            GROUP BY s.id, s.name
            ORDER BY total_value DESC
            LIMIT 5
        """)
        top_suppliers = [dict(row) for row in cursor.fetchall()]
        
        # Évolution mensuelle des commandes
        cursor.execute("""
            SELECT 
                strftime('%Y-%m', created_at) as month,
                COUNT(*) as order_count,
                SUM(CASE WHEN status = 'DELIVERED' THEN 1 ELSE 0 END) as delivered_count
            FROM order_headers
            WHERE created_at >= date('now', '-12 months')
            GROUP BY strftime('%Y-%m', created_at)
            ORDER BY month
        """)
        monthly_evolution = [dict(row) for row in cursor.fetchall()]
        
        # Temps moyen de livraison
        cursor.execute("""
            SELECT 
                AVG(julianday(r.receipt_date) - julianday(oh.order_date)) as avg_delivery_days
            FROM receipts r
            JOIN order_headers oh ON r.order_header_id = oh.id
            WHERE r.created_at >= date('now', '-30 days')
        """)
        avg_delivery = cursor.fetchone()['avg_delivery_days'] or 0
        
        db.close()
        
        return jsonify({
            'orders': orders_kpi,
            'costs': costs_kpi,
            'top_suppliers': top_suppliers,
            'monthly_evolution': monthly_evolution,
            'avg_delivery_days': round(avg_delivery, 1)
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/reports/cost-analysis', methods=['GET'])
def get_cost_analysis():
    """Analyse détaillée des coûts"""
    period = request.args.get('period', 'month')  # month, quarter, year
    
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Définir la période
        if period == 'month':
            date_filter = "date('now', 'start of month')"
        elif period == 'quarter':
            date_filter = "date('now', '-3 months')"
        else:
            date_filter = "date('now', 'start of year')"
        
        # Analyse par type de coût
        cursor.execute(f"""
            SELECT 
                'Customs Duties' as cost_type,
                SUM(cd.total_ttc) as total_amount,
                COUNT(DISTINCT lc.order_header_id) as order_count
            FROM customs_duties cd
            JOIN logistic_costs lc ON cd.logistic_cost_id = lc.id
            JOIN order_headers oh ON lc.order_header_id = oh.id
            WHERE oh.created_at >= {date_filter}
            
            UNION ALL
            
            SELECT 
                'Port Fees' as cost_type,
                SUM(pf.total_ttc) as total_amount,
                COUNT(DISTINCT lc.order_header_id) as order_count
            FROM port_fees pf
            JOIN logistic_costs lc ON pf.logistic_cost_id = lc.id
            JOIN order_headers oh ON lc.order_header_id = oh.id
            WHERE oh.created_at >= {date_filter}
            
            UNION ALL
            
            SELECT 
                'Shipping Fees' as cost_type,
                SUM(scf.total_ttc) as total_amount,
                COUNT(DISTINCT lc.order_header_id) as order_count
            FROM shipping_company_fees scf
            JOIN logistic_costs lc ON scf.logistic_cost_id = lc.id
            JOIN order_headers oh ON lc.order_header_id = oh.id
            WHERE oh.created_at >= {date_filter}
            
            UNION ALL
            
            SELECT 
                'Other Expenses' as cost_type,
                SUM(oe.total_ttc) as total_amount,
                COUNT(DISTINCT lc.order_header_id) as order_count
            FROM other_expenses oe
            JOIN logistic_costs lc ON oe.logistic_cost_id = lc.id
            JOIN order_headers oh ON lc.order_header_id = oh.id
            WHERE oh.created_at >= {date_filter}
        """)
        cost_breakdown = [dict(row) for row in cursor.fetchall()]
        
        # Calcul des pourcentages
        total_amount = sum(item['total_amount'] or 0 for item in cost_breakdown)
        for item in cost_breakdown:
            item['percentage'] = (item['total_amount'] or 0) / total_amount * 100 if total_amount > 0 else 0
        
        # Analyse par fournisseur
        cursor.execute(f"""
            SELECT 
                s.name as supplier_name,
                COUNT(oh.id) as order_count,
                SUM(r.total_landed_cost_dzd) as total_cost,
                AVG(r.total_landed_cost_dzd) as avg_cost_per_order
            FROM suppliers s
            JOIN order_headers oh ON s.id = oh.supplier_id
            LEFT JOIN receipts r ON oh.id = r.order_header_id
            WHERE oh.created_at >= {date_filter}
            GROUP BY s.id, s.name
            HAVING total_cost > 0
            ORDER BY total_cost DESC
        """)
        supplier_analysis = [dict(row) for row in cursor.fetchall()]
        
        # Évolution des coûts par mois
        cursor.execute(f"""
            SELECT 
                strftime('%Y-%m', r.created_at) as month,
                SUM(r.total_landed_cost_dzd) as total_cost,
                COUNT(*) as receipt_count,
                AVG(r.total_landed_cost_dzd) as avg_cost
            FROM receipts r
            WHERE r.created_at >= {date_filter}
            GROUP BY strftime('%Y-%m', r.created_at)
            ORDER BY month
        """)
        monthly_costs = [dict(row) for row in cursor.fetchall()]
        
        db.close()
        
        return jsonify({
            'period': period,
            'cost_breakdown': cost_breakdown,
            'supplier_analysis': supplier_analysis,
            'monthly_evolution': monthly_costs,
            'total_amount': total_amount
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/reports/inventory-valuation', methods=['GET'])
def get_inventory_valuation():
    """Rapport de valorisation des stocks"""
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Valorisation par article
        cursor.execute("""
            SELECT 
                oi.part_number,
                oi.description,
                s.name as supplier_name,
                SUM(ri.quantity_received) as total_received,
                AVG(ri.unit_landed_cost_dzd) as avg_unit_cost,
                SUM(ri.total_line_cost_dzd) as total_value,
                MAX(r.receipt_date) as last_receipt_date
            FROM order_items oi
            JOIN order_headers oh ON oi.order_header_id = oh.id
            JOIN suppliers s ON oh.supplier_id = s.id
            LEFT JOIN receipt_items ri ON oi.id = ri.order_item_id
            LEFT JOIN receipts r ON ri.receipt_id = r.id
            WHERE ri.quantity_received > 0
            GROUP BY oi.part_number, oi.description, s.name
            ORDER BY total_value DESC
        """)
        inventory_items = [dict(row) for row in cursor.fetchall()]
        
        # Résumé par fournisseur
        cursor.execute("""
            SELECT 
                s.name as supplier_name,
                COUNT(DISTINCT oi.part_number) as unique_parts,
                SUM(ri.quantity_received) as total_quantity,
                SUM(ri.total_line_cost_dzd) as total_value
            FROM suppliers s
            JOIN order_headers oh ON s.id = oh.supplier_id
            JOIN order_items oi ON oh.id = oi.order_header_id
            LEFT JOIN receipt_items ri ON oi.id = ri.order_item_id
            WHERE ri.quantity_received > 0
            GROUP BY s.id, s.name
            ORDER BY total_value DESC
        """)
        supplier_summary = [dict(row) for row in cursor.fetchall()]
        
        # Total de l'inventaire
        total_inventory_value = sum(item['total_value'] or 0 for item in inventory_items)
        total_items = len(inventory_items)
        
        db.close()
        
        return jsonify({
            'inventory_items': inventory_items,
            'supplier_summary': supplier_summary,
            'total_inventory_value': total_inventory_value,
            'total_unique_items': total_items
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/reports/export/<report_type>', methods=['GET'])
def export_report(report_type):
    """Exporter un rapport au format Excel"""
    try:
        if report_type == 'cost-analysis':
            # Récupérer les données d'analyse des coûts
            period = request.args.get('period', 'month')
            # Réutiliser la logique de get_cost_analysis()
            # ... (code similaire)
            
            # Créer le fichier Excel
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # Feuille principale
                df_main = pd.DataFrame([{'report_type': 'Cost Analysis', 'period': period}])
                df_main.to_excel(writer, sheet_name='Summary', index=False)
                
                # Autres feuilles selon le type de rapport
                # ...
            
            output.seek(0)
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f"rapport_{report_type}_{datetime.now().strftime('%Y%m%d')}.xlsx"
            )
        
        elif report_type == 'inventory':
            # Logique pour l'export d'inventaire
            pass
        
        else:
            return jsonify({'error': 'Type de rapport non supporté'}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/kpi/update', methods=['POST'])
def update_kpi():
    """Mettre à jour les KPI automatiquement"""
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Calculer les KPI pour le mois en cours
        current_month = datetime.now().replace(day=1)
        
        cursor.execute("""
            SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 'SHIPPED' THEN 1 END) as orders_shipped,
                COUNT(CASE WHEN status = 'DELIVERED' THEN 1 END) as orders_delivered,
                AVG(CASE 
                    WHEN status = 'DELIVERED' THEN 
                        julianday(updated_at) - julianday(order_date)
                    END) as avg_shipping_time
            FROM order_headers
            WHERE created_at >= date('now', 'start of month')
        """)
        orders_stats = cursor.fetchone()
        
        cursor.execute("""
            SELECT 
                SUM(total_landed_cost_dzd) as total_logistic_costs,
                AVG(CASE 
                    WHEN customs_clearance_date IS NOT NULL THEN
                        julianday(customs_clearance_date) - julianday(receipt_date)
                    END) as avg_clearance_time
            FROM receipts
            WHERE created_at >= date('now', 'start of month')
        """)
        costs_stats = cursor.fetchone()
        
        # Insérer ou mettre à jour les KPI
        cursor.execute("""
            INSERT OR REPLACE INTO kpi_logistics (
                date_period, total_orders, orders_shipped, orders_delivered,
                average_shipping_time_days, total_logistic_costs_dzd,
                average_customs_clearance_time_days
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            current_month.date(),
            orders_stats['total_orders'],
            orders_stats['orders_shipped'],
            orders_stats['orders_delivered'],
            round(orders_stats['avg_shipping_time'] or 0),
            costs_stats['total_logistic_costs'] or 0,
            round(costs_stats['avg_clearance_time'] or 0)
        ))
        
        db.commit()
        db.close()
        
        return jsonify({'message': 'KPI mis à jour avec succès'})
    
    except Exception as e:
        if db:
            db.rollback()
            db.close()
        return jsonify({'error': str(e)}), 500
