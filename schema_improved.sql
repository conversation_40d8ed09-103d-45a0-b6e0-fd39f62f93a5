-- Sc<PERSON><PERSON>ma amélioré pour la gestion logistique
-- Tables existantes (users, suppliers, order_headers, order_items) restent inchangées

-- Table principale des coûts logistiques améliorée
CREATE TABLE IF NOT EXISTS logistic_costs_main (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_header_id INTEGER NOT NULL,
    exchange_rate DECIMAL(15,5) NOT NULL,
    currency TEXT NOT NULL CHECK (currency IN ('USD', 'EURO', 'YUAN')),
    shipment_type TEXT NOT NULL CHECK (shipment_type IN ('SEA', 'AIR', 'Express', 'Other')),
    goods_price_currency DECIMAL(15,2),
    fob_amount DECIMAL(15,2),
    freight DECIMAL(15,2),
    total_amount_cif DECIMAL(15,2),
    fob_amount_dzd DECIMAL(15,2),
    freight_dzd DECIMAL(15,2),
    total_amount_cif_dzd DECIMAL(15,2),
    landed_cost_ht DECIMAL(15,2),
    landed_cost_coefficient DECIMAL(10,5),
    total_paid_ttc DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_header_id) REFERENCES order_headers(id) ON DELETE CASCADE
);

-- Droits de Douane améliorés
CREATE TABLE IF NOT EXISTS customs_duties_improved (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    logistic_cost_id INTEGER NOT NULL,
    cost_allocation_name TEXT DEFAULT 'ALGERIA CUSTOMS',
    d3_number TEXT NOT NULL,
    d3_date DATE NOT NULL,
    -- Quittance 1
    quittance1_number TEXT,
    quittance1_date DATE,
    quittance1_ttc DECIMAL(15,2) NOT NULL,
    quittance1_tva DECIMAL(15,2) NOT NULL,
    quittance1_ht DECIMAL(15,2),
    -- Quittance 2
    quittance2_number TEXT,
    quittance2_date DATE,
    quittance2_ttc DECIMAL(15,2),
    quittance2_tva DECIMAL(15,2),
    quittance2_ht DECIMAL(15,2),
    -- Totaux calculés
    total_ttc DECIMAL(15,2),
    total_tva DECIMAL(15,2),
    total_ht DECIMAL(15,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (logistic_cost_id) REFERENCES logistic_costs_main(id) ON DELETE CASCADE
);

-- Frais Portuaires améliorés
CREATE TABLE IF NOT EXISTS port_fees_improved (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    logistic_cost_id INTEGER NOT NULL,
    -- Frais de Livraison Import
    import_delivery_invoice TEXT,
    import_delivery_date DATE,
    import_delivery_ttc DECIMAL(15,2),
    import_delivery_tva DECIMAL(15,2),
    import_delivery_ht DECIMAL(15,2),
    -- Inspection Douanière
    customs_inspection_invoice TEXT,
    customs_inspection_date DATE,
    customs_inspection_ttc DECIMAL(15,2),
    customs_inspection_tva DECIMAL(15,2),
    customs_inspection_ht DECIMAL(15,2),
    -- Démurrage
    demurrage_invoice TEXT,
    demurrage_date DATE,
    demurrage_ttc DECIMAL(15,2),
    demurrage_tva DECIMAL(15,2),
    demurrage_ht DECIMAL(15,2),
    -- Totaux calculés
    total_ttc DECIMAL(15,2),
    total_tva DECIMAL(15,2),
    total_ht DECIMAL(15,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (logistic_cost_id) REFERENCES logistic_costs_main(id) ON DELETE CASCADE
);

-- Frais de Transporteur améliorés
CREATE TABLE IF NOT EXISTS shipping_company_fees_improved (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    logistic_cost_id INTEGER NOT NULL,
    -- Services d'Agence Maritime
    shipping_agency_invoice TEXT,
    shipping_agency_date DATE,
    shipping_agency_ttc DECIMAL(15,2),
    shipping_agency_tva DECIMAL(15,2),
    shipping_agency_ht DECIMAL(15,2),
    -- Retour des Conteneurs Vides
    empty_containers_invoice TEXT,
    empty_containers_date DATE,
    empty_containers_ttc DECIMAL(15,2),
    empty_containers_tva DECIMAL(15,2),
    empty_containers_ht DECIMAL(15,2),
    -- Démurrage
    demurrage_invoice TEXT,
    demurrage_date DATE,
    demurrage_ht DECIMAL(15,2),
    -- Totaux calculés
    total_ttc DECIMAL(15,2),
    total_tva DECIMAL(15,2),
    total_ht DECIMAL(15,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (logistic_cost_id) REFERENCES logistic_costs_main(id) ON DELETE CASCADE
);

-- Autres Frais Divers améliorés
CREATE TABLE IF NOT EXISTS other_expenses_improved (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    logistic_cost_id INTEGER NOT NULL,
    expense_type TEXT NOT NULL CHECK (expense_type IN (
        'OTHER MISCELLANEOUS EXPENSES', 
        'TRANSIT SERVICES EXPENSES'
    )),
    invoice_number TEXT,
    invoice_date DATE,
    total_ttc DECIMAL(15,2),
    tva DECIMAL(15,2),
    ht DECIMAL(15,2),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (logistic_cost_id) REFERENCES logistic_costs_main(id) ON DELETE CASCADE
);

-- Suivi Logistique amélioré
CREATE TABLE IF NOT EXISTS logistic_followups_improved (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_header_id INTEGER NOT NULL,
    voyage_number TEXT NOT NULL,
    call_at_port TEXT,
    bill_of_lading TEXT NOT NULL,
    vessel_name TEXT,
    shipowner TEXT,
    actual_time_of_arrival DATETIME,
    estimated_time_of_arrival DATETIME,
    customs_clearance_date DATE,
    delivery_date DATE,
    status TEXT NOT NULL CHECK (status IN (
        'EN ATTENTE', 'EN TRANSIT', 'ARRIVE AU PORT', 
        'EN DOUANE', 'LIVRE', 'RETARD', 'ANNULE'
    )) DEFAULT 'EN ATTENTE',
    -- Conteneurs
    containers_20 INTEGER DEFAULT 0,
    containers_40 INTEGER DEFAULT 0,
    total_packages INTEGER,
    -- Documents
    commercial_invoice_number TEXT,
    packing_list_number TEXT,
    certificate_of_origin TEXT,
    -- Statistiques
    transit_days INTEGER,
    customs_clearance_days INTEGER,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_header_id) REFERENCES order_headers(id) ON DELETE CASCADE
);

-- Triggers pour les calculs automatiques

-- Trigger pour calculer les montants HT dans customs_duties_improved
CREATE TRIGGER IF NOT EXISTS calc_customs_duties_ht
AFTER INSERT ON customs_duties_improved
FOR EACH ROW
BEGIN
    UPDATE customs_duties_improved SET
        quittance1_ht = NEW.quittance1_ttc - NEW.quittance1_tva,
        quittance2_ht = COALESCE(NEW.quittance2_ttc, 0) - COALESCE(NEW.quittance2_tva, 0),
        total_ttc = NEW.quittance1_ttc + COALESCE(NEW.quittance2_ttc, 0),
        total_tva = NEW.quittance1_tva + COALESCE(NEW.quittance2_tva, 0),
        total_ht = (NEW.quittance1_ttc - NEW.quittance1_tva) + (COALESCE(NEW.quittance2_ttc, 0) - COALESCE(NEW.quittance2_tva, 0))
    WHERE id = NEW.id;
END;

-- Trigger pour calculer les montants HT dans port_fees_improved
CREATE TRIGGER IF NOT EXISTS calc_port_fees_ht
AFTER INSERT ON port_fees_improved
FOR EACH ROW
BEGIN
    UPDATE port_fees_improved SET
        import_delivery_ht = COALESCE(NEW.import_delivery_ttc, 0) - COALESCE(NEW.import_delivery_tva, 0),
        customs_inspection_ht = COALESCE(NEW.customs_inspection_ttc, 0) - COALESCE(NEW.customs_inspection_tva, 0),
        demurrage_ht = COALESCE(NEW.demurrage_ttc, 0) - COALESCE(NEW.demurrage_tva, 0),
        total_ttc = COALESCE(NEW.import_delivery_ttc, 0) + COALESCE(NEW.customs_inspection_ttc, 0) + COALESCE(NEW.demurrage_ttc, 0),
        total_tva = COALESCE(NEW.import_delivery_tva, 0) + COALESCE(NEW.customs_inspection_tva, 0) + COALESCE(NEW.demurrage_tva, 0),
        total_ht = (COALESCE(NEW.import_delivery_ttc, 0) - COALESCE(NEW.import_delivery_tva, 0)) +
                   (COALESCE(NEW.customs_inspection_ttc, 0) - COALESCE(NEW.customs_inspection_tva, 0)) +
                   (COALESCE(NEW.demurrage_ttc, 0) - COALESCE(NEW.demurrage_tva, 0))
    WHERE id = NEW.id;
END;

-- Trigger pour calculer les montants HT dans shipping_company_fees_improved
CREATE TRIGGER IF NOT EXISTS calc_shipping_fees_ht
AFTER INSERT ON shipping_company_fees_improved
FOR EACH ROW
BEGIN
    UPDATE shipping_company_fees_improved SET
        shipping_agency_ht = COALESCE(NEW.shipping_agency_ttc, 0) - COALESCE(NEW.shipping_agency_tva, 0),
        empty_containers_ht = COALESCE(NEW.empty_containers_ttc, 0) - COALESCE(NEW.empty_containers_tva, 0),
        total_ttc = COALESCE(NEW.shipping_agency_ttc, 0) + COALESCE(NEW.empty_containers_ttc, 0),
        total_tva = COALESCE(NEW.shipping_agency_tva, 0) + COALESCE(NEW.empty_containers_tva, 0),
        total_ht = (COALESCE(NEW.shipping_agency_ttc, 0) - COALESCE(NEW.shipping_agency_tva, 0)) +
                   (COALESCE(NEW.empty_containers_ttc, 0) - COALESCE(NEW.empty_containers_tva, 0)) +
                   COALESCE(NEW.demurrage_ht, 0)
    WHERE id = NEW.id;
END;

-- Trigger pour calculer les montants HT dans other_expenses_improved
CREATE TRIGGER IF NOT EXISTS calc_other_expenses_ht
AFTER INSERT ON other_expenses_improved
FOR EACH ROW
BEGIN
    UPDATE other_expenses_improved SET
        ht = COALESCE(NEW.total_ttc, 0) - COALESCE(NEW.tva, 0)
    WHERE id = NEW.id;
END;

-- Trigger pour calculer les montants DZD dans logistic_costs_main
CREATE TRIGGER IF NOT EXISTS calc_dzd_amounts
AFTER INSERT ON logistic_costs_main
FOR EACH ROW
BEGIN
    UPDATE logistic_costs_main SET
        fob_amount_dzd = COALESCE(NEW.fob_amount, 0) * NEW.exchange_rate,
        freight_dzd = COALESCE(NEW.freight, 0) * NEW.exchange_rate,
        total_amount_cif_dzd = COALESCE(NEW.total_amount_cif, 0) * NEW.exchange_rate
    WHERE id = NEW.id;
END;

-- Trigger pour mettre à jour le Landed Cost
CREATE TRIGGER IF NOT EXISTS update_landed_cost
AFTER INSERT ON customs_duties_improved
FOR EACH ROW
BEGIN
    UPDATE logistic_costs_main SET
        landed_cost_ht = (
            COALESCE((SELECT total_ht FROM customs_duties_improved WHERE logistic_cost_id = NEW.logistic_cost_id), 0) +
            COALESCE((SELECT total_ht FROM port_fees_improved WHERE logistic_cost_id = NEW.logistic_cost_id), 0) +
            COALESCE((SELECT total_ht FROM shipping_company_fees_improved WHERE logistic_cost_id = NEW.logistic_cost_id), 0) +
            COALESCE((SELECT SUM(ht) FROM other_expenses_improved WHERE logistic_cost_id = NEW.logistic_cost_id), 0) +
            COALESCE(total_amount_cif_dzd, 0)
        ),
        landed_cost_coefficient = CASE
            WHEN total_amount_cif_dzd > 0 THEN landed_cost_ht / total_amount_cif_dzd
            ELSE 0
        END
    WHERE id = NEW.logistic_cost_id;
END;
