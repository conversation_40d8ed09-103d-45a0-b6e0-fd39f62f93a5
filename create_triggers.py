#!/usr/bin/env python3
"""
Script pour créer les triggers manuellement
"""

import sqlite3

def create_triggers():
    """Créer tous les triggers nécessaires"""
    try:
        conn = sqlite3.connect('logistics.db')
        
        triggers = [
            # Trigger pour customs_duties_improved
            """
            CREATE TRIGGER IF NOT EXISTS calc_customs_duties_ht
            AFTER INSERT ON customs_duties_improved
            FOR EACH ROW
            BEGIN
                UPDATE customs_duties_improved SET
                    quittance1_ht = NEW.quittance1_ttc - NEW.quittance1_tva,
                    quittance2_ht = COALESCE(NEW.quittance2_ttc, 0) - COALESCE(NEW.quittance2_tva, 0),
                    total_ttc = NEW.quittance1_ttc + COALESCE(NEW.quittance2_ttc, 0),
                    total_tva = NEW.quittance1_tva + COALESCE(NEW.quittance2_tva, 0),
                    total_ht = (NEW.quittance1_ttc - NEW.quittance1_tva) + (COALESCE(NEW.quittance2_ttc, 0) - COALESCE(NEW.quittance2_tva, 0))
                WHERE id = NEW.id;
            END;
            """,
            
            # Trigger pour port_fees_improved
            """
            CREATE TRIGGER IF NOT EXISTS calc_port_fees_ht
            AFTER INSERT ON port_fees_improved
            FOR EACH ROW
            BEGIN
                UPDATE port_fees_improved SET
                    import_delivery_ht = COALESCE(NEW.import_delivery_ttc, 0) - COALESCE(NEW.import_delivery_tva, 0),
                    customs_inspection_ht = COALESCE(NEW.customs_inspection_ttc, 0) - COALESCE(NEW.customs_inspection_tva, 0),
                    demurrage_ht = COALESCE(NEW.demurrage_ttc, 0) - COALESCE(NEW.demurrage_tva, 0),
                    total_ttc = COALESCE(NEW.import_delivery_ttc, 0) + COALESCE(NEW.customs_inspection_ttc, 0) + COALESCE(NEW.demurrage_ttc, 0),
                    total_tva = COALESCE(NEW.import_delivery_tva, 0) + COALESCE(NEW.customs_inspection_tva, 0) + COALESCE(NEW.demurrage_tva, 0),
                    total_ht = (COALESCE(NEW.import_delivery_ttc, 0) - COALESCE(NEW.import_delivery_tva, 0)) + 
                               (COALESCE(NEW.customs_inspection_ttc, 0) - COALESCE(NEW.customs_inspection_tva, 0)) + 
                               (COALESCE(NEW.demurrage_ttc, 0) - COALESCE(NEW.demurrage_tva, 0))
                WHERE id = NEW.id;
            END;
            """,
            
            # Trigger pour shipping_company_fees_improved
            """
            CREATE TRIGGER IF NOT EXISTS calc_shipping_fees_ht
            AFTER INSERT ON shipping_company_fees_improved
            FOR EACH ROW
            BEGIN
                UPDATE shipping_company_fees_improved SET
                    shipping_agency_ht = COALESCE(NEW.shipping_agency_ttc, 0) - COALESCE(NEW.shipping_agency_tva, 0),
                    empty_containers_ht = COALESCE(NEW.empty_containers_ttc, 0) - COALESCE(NEW.empty_containers_tva, 0),
                    total_ttc = COALESCE(NEW.shipping_agency_ttc, 0) + COALESCE(NEW.empty_containers_ttc, 0),
                    total_tva = COALESCE(NEW.shipping_agency_tva, 0) + COALESCE(NEW.empty_containers_tva, 0),
                    total_ht = (COALESCE(NEW.shipping_agency_ttc, 0) - COALESCE(NEW.shipping_agency_tva, 0)) + 
                               (COALESCE(NEW.empty_containers_ttc, 0) - COALESCE(NEW.empty_containers_tva, 0)) + 
                               COALESCE(NEW.demurrage_ht, 0)
                WHERE id = NEW.id;
            END;
            """,
            
            # Trigger pour other_expenses_improved
            """
            CREATE TRIGGER IF NOT EXISTS calc_other_expenses_ht
            AFTER INSERT ON other_expenses_improved
            FOR EACH ROW
            BEGIN
                UPDATE other_expenses_improved SET
                    ht = COALESCE(NEW.total_ttc, 0) - COALESCE(NEW.tva, 0)
                WHERE id = NEW.id;
            END;
            """,
            
            # Trigger pour logistic_costs_main
            """
            CREATE TRIGGER IF NOT EXISTS calc_dzd_amounts
            AFTER INSERT ON logistic_costs_main
            FOR EACH ROW
            BEGIN
                UPDATE logistic_costs_main SET
                    fob_amount_dzd = COALESCE(NEW.fob_amount, 0) * NEW.exchange_rate,
                    freight_dzd = COALESCE(NEW.freight, 0) * NEW.exchange_rate,
                    total_amount_cif_dzd = COALESCE(NEW.total_amount_cif, 0) * NEW.exchange_rate
                WHERE id = NEW.id;
            END;
            """
        ]
        
        for i, trigger in enumerate(triggers, 1):
            try:
                conn.execute(trigger)
                print(f"✅ Trigger {i} créé avec succès")
            except sqlite3.Error as e:
                print(f"❌ Erreur trigger {i}: {e}")
        
        conn.commit()
        conn.close()
        
        print("✅ Tous les triggers ont été créés!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        return False

def verify_triggers():
    """Vérifier que les triggers sont bien créés"""
    try:
        conn = sqlite3.connect('logistics.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='trigger'")
        triggers = cursor.fetchall()
        
        print(f"📊 Triggers trouvés: {len(triggers)}")
        for trigger in triggers:
            print(f"   ✅ {trigger[0]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

if __name__ == '__main__':
    print("🔧 Création des triggers...")
    if create_triggers():
        verify_triggers()
    else:
        print("❌ Échec de la création des triggers")
