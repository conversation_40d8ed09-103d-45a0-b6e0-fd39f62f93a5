{% extends "base.html" %}

{% block title %}Coûts Logistiques - Système de Gestion Logistique{% endblock %}
{% block page_title %}Coûts Logistiques{% endblock %}

{% block content %}
<!-- Actions Bar -->
<div class="row mb-4">
    <div class="col-md-6">
        <h4>Gestion des Coûts Logistiques</h4>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-primary me-2" onclick="showCreateCostModal()">
            <i class="fas fa-plus"></i> Nouveau Coût Logistique
        </button>
        <button class="btn btn-success me-2" onclick="showImportCostModal()">
            <i class="fas fa-file-import"></i> Importer Excel
        </button>
        <button class="btn btn-info" onclick="exportAllCosts()">
            <i class="fas fa-file-export"></i> Exporter Tout
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">Commande</label>
                <select class="form-select" id="filterOrder">
                    <option value="">Toutes les commandes</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Devise</label>
                <select class="form-select" id="filterCurrency">
                    <option value="">Toutes les devises</option>
                    <option value="USD">USD</option>
                    <option value="EURO">EURO</option>
                    <option value="YUAN">YUAN</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Type d'Expédition</label>
                <select class="form-select" id="filterShipmentType">
                    <option value="">Tous les types</option>
                    <option value="SEA">Maritime</option>
                    <option value="AIR">Aérien</option>
                    <option value="Express">Express</option>
                    <option value="Other">Autre</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Recherche</label>
                <input type="text" class="form-control" id="searchCost" placeholder="Rechercher...">
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-12">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search"></i> Filtrer
                </button>
                <button class="btn btn-secondary ms-2" onclick="clearFilters()">
                    <i class="fas fa-times"></i> Effacer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Costs Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="costsTable">
                <thead>
                    <tr>
                        <th>Commande</th>
                        <th>Devise</th>
                        <th>Taux Change</th>
                        <th>FOB (DZD)</th>
                        <th>Fret (DZD)</th>
                        <th>CIF Total (DZD)</th>
                        <th>Landed Cost HT</th>
                        <th>Coefficient</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="costsTableBody">
                    <tr>
                        <td colspan="9" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create Cost Modal -->
<div class="modal fade" id="createCostModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nouveau Coût Logistique</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createCostForm">
                    <!-- Informations Générales -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations Générales</h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label">Commande *</label>
                                    <select class="form-select" name="order_id" required>
                                        <option value="">Sélectionner une commande</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Devise *</label>
                                    <select class="form-select" name="currency" required onchange="updateExchangeRate()">
                                        <option value="USD">USD</option>
                                        <option value="EURO">EURO</option>
                                        <option value="YUAN">YUAN</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Taux de Change (5 décimales) *</label>
                                    <input type="number" class="form-control" name="exchange_rate" step="0.00001" value="135.50000" required onchange="calculateAmounts()">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Type d'Expédition *</label>
                                    <select class="form-select" name="shipment_type" required>
                                        <option value="SEA">Maritime</option>
                                        <option value="AIR">Aérien</option>
                                        <option value="Express">Express</option>
                                        <option value="Other">Autre</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Prix Marchandises (Devise)</label>
                                    <input type="number" class="form-control" name="goods_price_currency" step="0.01">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Coûts Principaux -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>Coûts Principaux</h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label">Prix FOB</label>
                                    <input type="number" class="form-control" name="fob_amount" step="0.01" onchange="calculateAmounts()">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Fret</label>
                                    <input type="number" class="form-control" name="freight" step="0.01" onchange="calculateAmounts()">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Total CIF</label>
                                    <input type="number" class="form-control" name="total_amount_cif" readonly>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label">FOB (DZD)</label>
                                    <input type="number" class="form-control" name="fob_amount_dzd" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Fret (DZD)</label>
                                    <input type="number" class="form-control" name="freight_dzd" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Total CIF (DZD)</label>
                                    <input type="number" class="form-control" name="total_amount_cif_dzd" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="showCostDetailsModal()">Continuer vers les Détails</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let orders = [];
    let costs = [];

    // Initialisation
    document.addEventListener('DOMContentLoaded', function() {
        loadOrders();
        loadCosts();
    });

    async function loadOrders() {
        try {
            const response = await apiRequest('/api/orders');
            orders = response.orders;

            // Remplir le select des commandes
            const select = document.querySelector('select[name="order_id"]');
            select.innerHTML = '<option value="">Sélectionner une commande</option>';

            orders.forEach(order => {
                const option = document.createElement('option');
                option.value = order.id;
                option.textContent = `${order.order_number} - ${order.supplier_name}`;
                select.appendChild(option);
            });
        } catch (error) {
            console.error('Erreur lors du chargement des commandes:', error);
        }
    }

    async function loadCosts() {
        try {
            const response = await apiRequest('/api/costs-improved');
            costs = response.costs;
            displayCosts(costs);
        } catch (error) {
            console.error('Erreur lors du chargement des coûts:', error);
            document.getElementById('costsTableBody').innerHTML =
                '<tr><td colspan="9" class="text-center text-danger">Erreur de chargement</td></tr>';
        }
    }

    function displayCosts(costsToShow) {
        const tbody = document.getElementById('costsTableBody');

        if (costsToShow.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">Aucun coût logistique trouvé</td></tr>';
            return;
        }

        tbody.innerHTML = costsToShow.map(cost => `
            <tr>
                <td><strong>${cost.order_number}</strong></td>
                <td>${cost.currency}</td>
                <td>${cost.exchange_rate.toFixed(5)}</td>
                <td>${formatCurrency(cost.fob_amount_dzd || 0)}</td>
                <td>${formatCurrency(cost.freight_dzd || 0)}</td>
                <td>${formatCurrency(cost.total_amount_cif_dzd || 0)}</td>
                <td>${formatCurrency(cost.landed_cost_ht || 0)}</td>
                <td>${(cost.landed_cost_coefficient || 0).toFixed(5)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewCostDetails(${cost.id})" title="Voir">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editCost(${cost.id})" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="exportCost(${cost.id})" title="Exporter">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteCost(${cost.id})" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    function showCreateCostModal() {
        document.getElementById('createCostForm').reset();
        const modal = new bootstrap.Modal(document.getElementById('createCostModal'));
        modal.show();
    }

    function calculateAmounts() {
        const fob = parseFloat(document.querySelector('input[name="fob_amount"]').value) || 0;
        const freight = parseFloat(document.querySelector('input[name="freight"]').value) || 0;
        const exchangeRate = parseFloat(document.querySelector('input[name="exchange_rate"]').value) || 1;

        // Calculer CIF
        const cif = fob + freight;
        document.querySelector('input[name="total_amount_cif"]').value = cif.toFixed(2);

        // Calculer montants DZD
        document.querySelector('input[name="fob_amount_dzd"]').value = (fob * exchangeRate).toFixed(2);
        document.querySelector('input[name="freight_dzd"]').value = (freight * exchangeRate).toFixed(2);
        document.querySelector('input[name="total_amount_cif_dzd"]').value = (cif * exchangeRate).toFixed(2);
    }

    function updateExchangeRate() {
        const currency = document.querySelector('select[name="currency"]').value;
        const exchangeRateInput = document.querySelector('input[name="exchange_rate"]');

        // Taux de change par défaut
        const defaultRates = {
            'USD': 135.50000,
            'EURO': 145.75000,
            'YUAN': 19.25000
        };

        if (defaultRates[currency]) {
            exchangeRateInput.value = defaultRates[currency];
            calculateAmounts();
        }
    }

    function showCostDetailsModal() {
        // Cette fonction ouvrira un second modal pour les détails des coûts
        alert('Modal des détails des coûts à implémenter dans la prochaine étape');
    }

    async function viewCostDetails(costId) {
        try {
            const response = await apiRequest(`/api/costs-improved/${costId}/details`);
            showCostDetailsView(response);
        } catch (error) {
            alert('Erreur lors du chargement des détails: ' + error.message);
        }
    }

    function showCostDetailsView(costData) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Détails du Coût Logistique - ${costData.main_cost.order_number}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Commande:</strong> ${costData.main_cost.order_number}<br>
                                <strong>Fournisseur:</strong> ${costData.main_cost.supplier_name}<br>
                                <strong>Devise:</strong> ${costData.main_cost.currency}<br>
                                <strong>Taux de change:</strong> ${costData.main_cost.exchange_rate}
                            </div>
                            <div class="col-md-6">
                                <strong>FOB (DZD):</strong> ${formatCurrency(costData.main_cost.fob_amount_dzd)}<br>
                                <strong>Fret (DZD):</strong> ${formatCurrency(costData.main_cost.freight_dzd)}<br>
                                <strong>CIF Total (DZD):</strong> ${formatCurrency(costData.main_cost.total_amount_cif_dzd)}<br>
                                <strong>Landed Cost HT:</strong> ${formatCurrency(costData.main_cost.landed_cost_ht)}
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                        <button type="button" class="btn btn-success" onclick="exportCost(${costData.main_cost.id})">Exporter Excel</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    async function exportCost(costId) {
        try {
            showLoading();
            const response = await fetch(`/api/costs-improved/${costId}/export`);

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `couts_logistiques_${costId}.xlsx`;
                a.click();
                window.URL.revokeObjectURL(url);
            } else {
                throw new Error('Erreur lors de l\'export');
            }
        } catch (error) {
            alert('Erreur lors de l\'export: ' + error.message);
        } finally {
            hideLoading();
        }
    }

    function applyFilters() {
        let filteredCosts = [...costs];

        const orderFilter = document.getElementById('filterOrder').value;
        const currencyFilter = document.getElementById('filterCurrency').value;
        const shipmentFilter = document.getElementById('filterShipmentType').value;
        const search = document.getElementById('searchCost').value.toLowerCase();

        if (orderFilter) {
            filteredCosts = filteredCosts.filter(cost => cost.order_header_id == orderFilter);
        }

        if (currencyFilter) {
            filteredCosts = filteredCosts.filter(cost => cost.currency === currencyFilter);
        }

        if (shipmentFilter) {
            filteredCosts = filteredCosts.filter(cost => cost.shipment_type === shipmentFilter);
        }

        if (search) {
            filteredCosts = filteredCosts.filter(cost =>
                cost.order_number.toLowerCase().includes(search) ||
                cost.supplier_name.toLowerCase().includes(search)
            );
        }

        displayCosts(filteredCosts);
    }

    function clearFilters() {
        document.getElementById('filterOrder').value = '';
        document.getElementById('filterCurrency').value = '';
        document.getElementById('filterShipmentType').value = '';
        document.getElementById('searchCost').value = '';
        displayCosts(costs);
    }
</script>
{% endblock %}
