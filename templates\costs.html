{% extends "base.html" %}

{% block title %}Coûts Logistiques - Système de Gestion Logistique{% endblock %}
{% block page_title %}Coûts Logistiques{% endblock %}

{% block content %}
<!-- Actions Bar -->
<div class="row mb-4">
    <div class="col-md-6">
        <h4>Gestion des Coûts Logistiques</h4>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-primary me-2" onclick="showCreateCostModal()">
            <i class="fas fa-plus"></i> Nouveau Coût Logistique
        </button>
        <button class="btn btn-success me-2" onclick="showImportCostModal()">
            <i class="fas fa-file-import"></i> Importer Excel
        </button>
        <button class="btn btn-info" onclick="exportAllCosts()">
            <i class="fas fa-file-export"></i> Exporter Tout
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">Commande</label>
                <select class="form-select" id="filterOrder">
                    <option value="">Toutes les commandes</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Devise</label>
                <select class="form-select" id="filterCurrency">
                    <option value="">Toutes les devises</option>
                    <option value="USD">USD</option>
                    <option value="EURO">EURO</option>
                    <option value="YUAN">YUAN</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Type d'Expédition</label>
                <select class="form-select" id="filterShipmentType">
                    <option value="">Tous les types</option>
                    <option value="SEA">Maritime</option>
                    <option value="AIR">Aérien</option>
                    <option value="Express">Express</option>
                    <option value="Other">Autre</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Recherche</label>
                <input type="text" class="form-control" id="searchCost" placeholder="Rechercher...">
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-12">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search"></i> Filtrer
                </button>
                <button class="btn btn-secondary ms-2" onclick="clearFilters()">
                    <i class="fas fa-times"></i> Effacer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Costs Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="costsTable">
                <thead>
                    <tr>
                        <th>Commande</th>
                        <th>Devise</th>
                        <th>Taux Change</th>
                        <th>FOB (DZD)</th>
                        <th>Fret (DZD)</th>
                        <th>CIF Total (DZD)</th>
                        <th>Landed Cost HT</th>
                        <th>Coefficient</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="costsTableBody">
                    <tr>
                        <td colspan="9" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
