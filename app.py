from flask import Flask, render_template, request, jsonify, session, redirect, url_for, g, send_file
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
import sqlite3
import pandas as pd
from io import BytesIO
from datetime import datetime, date
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# Configuration de la base de données
DATABASE = 'logistics.db'

def get_db():
    """Obtenir une connexion à la base de données"""
    db = sqlite3.connect(DATABASE)
    db.row_factory = sqlite3.Row
    return db

def init_db():
    """Initialiser la base de données avec toutes les tables"""
    with app.app_context():
        db = get_db()
        with app.open_resource('schema.sql', mode='r') as f:
            db.cursor().executescript(f.read())
        db.commit()
        db.close()

@app.before_request
def load_logged_in_user():
    """Charger l'utilisateur connecté avant chaque requête"""
    user_id = session.get('user_id')
    if user_id is None:
        g.user = None
    else:
        db = get_db()
        g.user = db.execute(
            'SELECT * FROM users WHERE id = ?', (user_id,)
        ).fetchone()
        db.close()

def login_required(f):
    """Décorateur pour les routes nécessitant une authentification"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if g.user is None:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# Routes d'authentification
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        db = get_db()
        error = None
        user = db.execute(
            'SELECT * FROM users WHERE username = ?', (username,)
        ).fetchone()
        
        if user is None:
            error = 'Nom d\'utilisateur incorrect.'
        elif not check_password_hash(user['password'], password):
            error = 'Mot de passe incorrect.'
        
        if error is None:
            session.clear()
            session['user_id'] = user['id']
            db.close()
            return redirect(url_for('dashboard'))
        
        db.close()
        return render_template('auth/login.html', error=error)
    
    return render_template('auth/login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        email = request.form['email']
        
        db = get_db()
        error = None
        
        if not username:
            error = 'Le nom d\'utilisateur est requis.'
        elif not password:
            error = 'Le mot de passe est requis.'
        elif db.execute(
            'SELECT id FROM users WHERE username = ?', (username,)
        ).fetchone() is not None:
            error = f'L\'utilisateur {username} existe déjà.'
        
        if error is None:
            db.execute(
                'INSERT INTO users (username, password, email) VALUES (?, ?, ?)',
                (username, generate_password_hash(password), email)
            )
            db.commit()
            db.close()
            return redirect(url_for('login'))
        
        db.close()
        return render_template('auth/register.html', error=error)
    
    return render_template('auth/register.html')

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

# Import des blueprints
from routes.orders import orders_bp
from routes.logistics import logistics_bp
from routes.receipts import receipts_bp
from routes.reports import reports_bp

# Enregistrement des blueprints
app.register_blueprint(orders_bp, url_prefix='/api')
app.register_blueprint(logistics_bp, url_prefix='/api')
app.register_blueprint(receipts_bp, url_prefix='/api')
app.register_blueprint(reports_bp, url_prefix='/api')

# Route principale
@app.route('/')
@login_required
def dashboard():
    return render_template('dashboard.html')

# Routes pour les fournisseurs
@app.route('/api/suppliers', methods=['GET'])
@login_required
def get_suppliers():
    try:
        db = get_db()
        cursor = db.cursor()
        cursor.execute("SELECT * FROM suppliers ORDER BY name")
        suppliers = [dict(row) for row in cursor.fetchall()]
        db.close()
        return jsonify({'suppliers': suppliers})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/suppliers', methods=['POST'])
@login_required
def create_supplier():
    data = request.get_json()
    if not data or 'name' not in data:
        return jsonify({'error': 'Nom du fournisseur requis'}), 400

    try:
        db = get_db()
        cursor = db.cursor()
        cursor.execute("""
            INSERT INTO suppliers (name, contact_person, email, phone, address, country)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            data['name'],
            data.get('contact_person'),
            data.get('email'),
            data.get('phone'),
            data.get('address'),
            data.get('country')
        ))
        supplier_id = cursor.lastrowid
        db.commit()
        db.close()
        return jsonify({'message': 'Fournisseur créé', 'id': supplier_id}), 201
    except Exception as e:
        if db:
            db.rollback()
            db.close()
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    if not os.path.exists(DATABASE):
        init_db()
    app.run(debug=True)
