{% extends "base.html" %}

{% block title %}Gestion des Commandes - Système de Gestion Logistique{% endblock %}
{% block page_title %}Gestion des Commandes{% endblock %}

{% block content %}
<!-- Actions Bar -->
<div class="row mb-4">
    <div class="col-md-6">
        <h4>Liste des Commandes</h4>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-primary me-2" onclick="showCreateOrderModal()">
            <i class="fas fa-plus"></i> Nouvelle Commande
        </button>
        <button class="btn btn-success me-2" onclick="showImportModal()">
            <i class="fas fa-file-import"></i> Importer Excel
        </button>
        <button class="btn btn-info" onclick="exportAllOrders()">
            <i class="fas fa-file-export"></i> Exporter Tout
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">Statut</label>
                <select class="form-select" id="filterStatus">
                    <option value="">Tous les statuts</option>
                    <option value="DRAFT">Brouillon</option>
                    <option value="CONFIRMED">Confirmée</option>
                    <option value="SHIPPED">Expédiée</option>
                    <option value="DELIVERED">Livrée</option>
                    <option value="CANCELLED">Annulée</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Fournisseur</label>
                <select class="form-select" id="filterSupplier">
                    <option value="">Tous les fournisseurs</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Date de</label>
                <input type="date" class="form-control" id="filterDateFrom">
            </div>
            <div class="col-md-3">
                <label class="form-label">Date à</label>
                <input type="date" class="form-control" id="filterDateTo">
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-6">
                <input type="text" class="form-control" id="searchOrder" placeholder="Rechercher par numéro de commande...">
            </div>
            <div class="col-md-6">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search"></i> Filtrer
                </button>
                <button class="btn btn-secondary ms-2" onclick="clearFilters()">
                    <i class="fas fa-times"></i> Effacer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Orders Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="ordersTable">
                <thead>
                    <tr>
                        <th>N° Commande</th>
                        <th>Date</th>
                        <th>Fournisseur</th>
                        <th>Devise</th>
                        <th>Montant Total</th>
                        <th>Articles</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="ordersTableBody">
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create Order Modal -->
<div class="modal fade" id="createOrderModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nouvelle Commande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createOrderForm">
                    <!-- Header Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Numéro de Commande *</label>
                            <input type="text" class="form-control" name="order_number" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Date de Commande *</label>
                            <input type="date" class="form-control" name="order_date" required>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Fournisseur *</label>
                            <select class="form-select" name="supplier_id" required>
                                <option value="">Sélectionner un fournisseur</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Numéro Odoo</label>
                            <input type="text" class="form-control" name="odoo_order_number">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">Type d'Opération *</label>
                            <select class="form-select" name="operation_type" required>
                                <option value="">Sélectionner</option>
                                <option value="Operational Expenses (OpEx)">Operational Expenses (OpEx)</option>
                                <option value="Investment (or Equipment)">Investment (or Equipment)</option>
                                <option value="Reinvestment">Reinvestment</option>
                                <option value="Raw Materials and Semi-finished Products">Raw Materials and Semi-finished Products</option>
                                <option value="Spare Parts">Spare Parts</option>
                                <option value="Goods for Resale as Is">Goods for Resale as Is</option>
                                <option value="Services (intangible)">Services (intangible)</option>
                                <option value="Temporary Importation">Temporary Importation</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Description Marchandises *</label>
                            <select class="form-select" name="description_goods" required>
                                <option value="">Sélectionner</option>
                                <option value="SPARE PARTS FOR VEHICLES">SPARE PARTS FOR VEHICLES</option>
                                <option value="LUBRICANT">LUBRICANT</option>
                                <option value="ACCESSORY">ACCESSORY</option>
                                <option value="OTHER">OTHER</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Devise *</label>
                            <select class="form-select" name="currency" required>
                                <option value="USD">USD</option>
                                <option value="EURO">EURO</option>
                                <option value="YUAN">YUAN</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">Taux de Change</label>
                            <input type="number" class="form-control" name="exchange_rate" step="0.00001" value="135.50000">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">De</label>
                            <input type="text" class="form-control" name="from_location">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">À</label>
                            <input type="text" class="form-control" name="to_location">
                        </div>
                    </div>
                    
                    <!-- Items Section -->
                    <hr>
                    <h6>Articles de la Commande</h6>
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-success" onclick="addOrderItem()">
                            <i class="fas fa-plus"></i> Ajouter un Article
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-sm" id="orderItemsTable">
                            <thead>
                                <tr>
                                    <th>N°</th>
                                    <th>Référence</th>
                                    <th>Description</th>
                                    <th>Quantité</th>
                                    <th>Prix FOB</th>
                                    <th>Montant</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="orderItemsTableBody">
                                <!-- Items will be added dynamically -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="saveOrder()">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Importer des Commandes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Fichier Excel</label>
                    <input type="file" class="form-control" id="importFile" accept=".xlsx,.xls">
                    <div class="form-text">
                        Le fichier doit contenir les feuilles "Header" et "Items" selon le format spécifié.
                    </div>
                </div>
                <div class="alert alert-info">
                    <h6>Format requis :</h6>
                    <ul class="mb-0">
                        <li><strong>Feuille "Header" :</strong> numero_commande, order_date, supplier_name, etc.</li>
                        <li><strong>Feuille "Items" :</strong> ITEM, #PART_NUMBER#, DESCRIPTION, Qty, U_FOB</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="importOrders()">Importer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let suppliers = [];
    let orders = [];
    let itemCounter = 0;
    
    // Initialisation
    document.addEventListener('DOMContentLoaded', function() {
        loadSuppliers();
        loadOrders();
    });
    
    async function loadSuppliers() {
        try {
            const response = await apiRequest('/api/suppliers');
            suppliers = response.suppliers;
            
            // Remplir les selects
            const selects = document.querySelectorAll('select[name="supplier_id"], #filterSupplier');
            selects.forEach(select => {
                if (select.id === 'filterSupplier') {
                    select.innerHTML = '<option value="">Tous les fournisseurs</option>';
                } else {
                    select.innerHTML = '<option value="">Sélectionner un fournisseur</option>';
                }
                
                suppliers.forEach(supplier => {
                    const option = document.createElement('option');
                    option.value = supplier.id;
                    option.textContent = supplier.name;
                    select.appendChild(option);
                });
            });
        } catch (error) {
            console.error('Erreur lors du chargement des fournisseurs:', error);
        }
    }
    
    async function loadOrders() {
        try {
            const response = await apiRequest('/api/orders');
            orders = response.orders;
            displayOrders(orders);
        } catch (error) {
            console.error('Erreur lors du chargement des commandes:', error);
            document.getElementById('ordersTableBody').innerHTML = 
                '<tr><td colspan="8" class="text-center text-danger">Erreur de chargement</td></tr>';
        }
    }
    
    function displayOrders(ordersToShow) {
        const tbody = document.getElementById('ordersTableBody');
        
        if (ordersToShow.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">Aucune commande trouvée</td></tr>';
            return;
        }
        
        tbody.innerHTML = ordersToShow.map(order => `
            <tr>
                <td><strong>${order.order_number}</strong></td>
                <td>${formatDate(order.order_date)}</td>
                <td>${order.supplier_name || 'N/A'}</td>
                <td>${order.currency}</td>
                <td>${formatCurrency(order.total_amount || 0, order.currency)}</td>
                <td><span class="badge bg-info">${order.items_count || 0}</span></td>
                <td><span class="badge bg-${getStatusColor(order.status)}">${order.status}</span></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewOrder(${order.id})" title="Voir">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editOrder(${order.id})" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="exportOrder(${order.id})" title="Exporter">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteOrder(${order.id})" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    function showCreateOrderModal() {
        document.getElementById('createOrderForm').reset();
        document.getElementById('orderItemsTableBody').innerHTML = '';
        itemCounter = 0;
        addOrderItem(); // Ajouter une première ligne

        // Définir la date d'aujourd'hui par défaut
        document.querySelector('input[name="order_date"]').value = new Date().toISOString().split('T')[0];

        const modal = new bootstrap.Modal(document.getElementById('createOrderModal'));
        modal.show();
    }

    function showImportModal() {
        document.getElementById('importFile').value = '';
        const modal = new bootstrap.Modal(document.getElementById('importModal'));
        modal.show();
    }

    async function importOrders() {
        const fileInput = document.getElementById('importFile');
        const file = fileInput.files[0];

        if (!file) {
            alert('Veuillez sélectionner un fichier Excel');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        try {
            showLoading();
            const response = await fetch('/api/orders/import', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok) {
                const modal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
                modal.hide();
                loadOrders();
                alert('Commandes importées avec succès !');
            } else {
                alert('Erreur lors de l\'import: ' + result.error);
            }
        } catch (error) {
            alert('Erreur lors de l\'import: ' + error.message);
        } finally {
            hideLoading();
        }
    }

    async function exportAllOrders() {
        try {
            showLoading();
            const response = await fetch('/api/orders/export-all');

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `toutes_commandes_${new Date().toISOString().split('T')[0]}.xlsx`;
                a.click();
                window.URL.revokeObjectURL(url);
            } else {
                throw new Error('Erreur lors de l\'export');
            }
        } catch (error) {
            alert('Erreur lors de l\'export: ' + error.message);
        } finally {
            hideLoading();
        }
    }
    
    function addOrderItem() {
        itemCounter++;
        const tbody = document.getElementById('orderItemsTableBody');
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${itemCounter}</td>
            <td><input type="text" class="form-control form-control-sm" name="part_number_${itemCounter}" required></td>
            <td><input type="text" class="form-control form-control-sm" name="description_${itemCounter}" required></td>
            <td><input type="number" class="form-control form-control-sm" name="quantity_${itemCounter}" min="1" required onchange="calculateAmount(${itemCounter})"></td>
            <td><input type="number" class="form-control form-control-sm" name="u_fob_${itemCounter}" step="0.01" required onchange="calculateAmount(${itemCounter})"></td>
            <td><span id="amount_${itemCounter}">0.00</span></td>
            <td><button type="button" class="btn btn-sm btn-danger" onclick="removeOrderItem(this)"><i class="fas fa-trash"></i></button></td>
        `;
        tbody.appendChild(row);
    }
    
    function removeOrderItem(button) {
        button.closest('tr').remove();
    }
    
    function calculateAmount(itemNumber) {
        const quantity = parseFloat(document.querySelector(`input[name="quantity_${itemNumber}"]`).value) || 0;
        const unitPrice = parseFloat(document.querySelector(`input[name="u_fob_${itemNumber}"]`).value) || 0;
        const amount = quantity * unitPrice;
        document.getElementById(`amount_${itemNumber}`).textContent = amount.toFixed(2);
    }
    
    async function saveOrder() {
        const form = document.getElementById('createOrderForm');
        const formData = new FormData(form);
        const isEdit = form.dataset.editId;

        // Construire l'objet de données
        const orderData = {
            header: {
                order_number: formData.get('order_number'),
                order_date: formData.get('order_date'),
                supplier_id: parseInt(formData.get('supplier_id')),
                odoo_order_number: formData.get('odoo_order_number'),
                operation_type: formData.get('operation_type'),
                description_goods: formData.get('description_goods'),
                currency: formData.get('currency'),
                exchange_rate: parseFloat(formData.get('exchange_rate')),
                from_location: formData.get('from_location'),
                to_location: formData.get('to_location'),
                notes: formData.get('notes')
            },
            items: []
        };

        // Récupérer les articles
        const tbody = document.getElementById('orderItemsTableBody');
        const rows = tbody.querySelectorAll('tr');

        rows.forEach((row, index) => {
            const itemNumber = index + 1;
            const partNumber = row.querySelector(`input[name="part_number_${itemNumber}"]`)?.value;
            const description = row.querySelector(`input[name="description_${itemNumber}"]`)?.value;
            const quantity = row.querySelector(`input[name="quantity_${itemNumber}"]`)?.value;
            const uFob = row.querySelector(`input[name="u_fob_${itemNumber}"]`)?.value;

            if (partNumber && description && quantity && uFob) {
                orderData.items.push({
                    item_number: itemNumber,
                    part_number: partNumber,
                    description: description,
                    quantity: parseInt(quantity),
                    u_fob: parseFloat(uFob)
                });
            }
        });

        try {
            const url = isEdit ? `/api/orders/${isEdit}` : '/api/orders';
            const method = isEdit ? 'PUT' : 'POST';

            await apiRequest(url, {
                method: method,
                body: JSON.stringify(orderData)
            });

            const modal = bootstrap.Modal.getInstance(document.getElementById('createOrderModal'));
            modal.hide();

            // Nettoyer le formulaire
            delete form.dataset.editId;

            loadOrders(); // Recharger la liste
            alert(isEdit ? 'Commande modifiée avec succès !' : 'Commande créée avec succès !');
        } catch (error) {
            alert('Erreur lors de la sauvegarde: ' + error.message);
        }
    }
    
    function getStatusColor(status) {
        const colors = {
            'DRAFT': 'secondary',
            'CONFIRMED': 'info',
            'SHIPPED': 'warning',
            'DELIVERED': 'success',
            'CANCELLED': 'danger'
        };
        return colors[status] || 'secondary';
    }
    
    async function exportOrder(orderId) {
        try {
            const response = await fetch(`/api/orders/${orderId}/export`);
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `commande_${orderId}.xlsx`;
                a.click();
                window.URL.revokeObjectURL(url);
            } else {
                throw new Error('Erreur lors de l\'export');
            }
        } catch (error) {
            alert('Erreur lors de l\'export: ' + error.message);
        }
    }
    
    async function deleteOrder(orderId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette commande ?')) {
            try {
                await apiRequest(`/api/orders/${orderId}`, { method: 'DELETE' });
                loadOrders();
                alert('Commande supprimée avec succès !');
            } catch (error) {
                alert('Erreur lors de la suppression: ' + error.message);
            }
        }
    }
    
    function applyFilters() {
        let filteredOrders = [...orders];
        
        const status = document.getElementById('filterStatus').value;
        const supplier = document.getElementById('filterSupplier').value;
        const dateFrom = document.getElementById('filterDateFrom').value;
        const dateTo = document.getElementById('filterDateTo').value;
        const search = document.getElementById('searchOrder').value.toLowerCase();
        
        if (status) {
            filteredOrders = filteredOrders.filter(order => order.status === status);
        }
        
        if (supplier) {
            filteredOrders = filteredOrders.filter(order => order.supplier_id == supplier);
        }
        
        if (search) {
            filteredOrders = filteredOrders.filter(order => 
                order.order_number.toLowerCase().includes(search)
            );
        }
        
        displayOrders(filteredOrders);
    }
    
    function clearFilters() {
        document.getElementById('filterStatus').value = '';
        document.getElementById('filterSupplier').value = '';
        document.getElementById('filterDateFrom').value = '';
        document.getElementById('filterDateTo').value = '';
        document.getElementById('searchOrder').value = '';
        displayOrders(orders);
    }

    async function viewOrder(orderId) {
        try {
            const response = await apiRequest(`/api/orders/${orderId}`);
            showOrderDetailsModal(response);
        } catch (error) {
            alert('Erreur lors du chargement des détails: ' + error.message);
        }
    }

    function showOrderDetailsModal(orderData) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Détails de la Commande ${orderData.header.order_number}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Fournisseur:</strong> ${orderData.header.supplier_name}<br>
                                <strong>Date:</strong> ${formatDate(orderData.header.order_date)}<br>
                                <strong>Statut:</strong> <span class="badge bg-${getStatusColor(orderData.header.status)}">${orderData.header.status}</span>
                            </div>
                            <div class="col-md-6">
                                <strong>Devise:</strong> ${orderData.header.currency}<br>
                                <strong>Taux de change:</strong> ${orderData.header.exchange_rate}<br>
                                <strong>De:</strong> ${orderData.header.from_location || 'N/A'} → <strong>À:</strong> ${orderData.header.to_location || 'N/A'}
                            </div>
                        </div>
                        <h6>Articles de la commande</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>N°</th>
                                        <th>Référence</th>
                                        <th>Description</th>
                                        <th>Quantité</th>
                                        <th>Prix FOB</th>
                                        <th>Montant</th>
                                        <th>Reçu</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${orderData.items.map(item => `
                                        <tr>
                                            <td>${item.item_number}</td>
                                            <td>${item.part_number}</td>
                                            <td>${item.description}</td>
                                            <td>${item.quantity}</td>
                                            <td>${formatCurrency(item.u_fob, orderData.header.currency)}</td>
                                            <td>${formatCurrency(item.amount, orderData.header.currency)}</td>
                                            <td>${item.received_quantity || 0}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                        ${orderData.header.notes ? `<div class="mt-3"><strong>Notes:</strong><br>${orderData.header.notes}</div>` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                        <button type="button" class="btn btn-warning" onclick="editOrder(${orderData.header.id})">Modifier</button>
                        <button type="button" class="btn btn-success" onclick="exportOrder(${orderData.header.id})">Exporter</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    async function editOrder(orderId) {
        try {
            const response = await apiRequest(`/api/orders/${orderId}`);
            populateEditForm(response);
            const modal = new bootstrap.Modal(document.getElementById('createOrderModal'));
            modal.show();
        } catch (error) {
            alert('Erreur lors du chargement de la commande: ' + error.message);
        }
    }

    function populateEditForm(orderData) {
        // Remplir les champs de l'entête
        const form = document.getElementById('createOrderForm');
        form.querySelector('input[name="order_number"]').value = orderData.header.order_number;
        form.querySelector('input[name="order_date"]').value = orderData.header.order_date;
        form.querySelector('select[name="supplier_id"]').value = orderData.header.supplier_id;
        form.querySelector('input[name="odoo_order_number"]').value = orderData.header.odoo_order_number || '';
        form.querySelector('select[name="operation_type"]').value = orderData.header.operation_type;
        form.querySelector('select[name="description_goods"]').value = orderData.header.description_goods;
        form.querySelector('select[name="currency"]').value = orderData.header.currency;
        form.querySelector('input[name="exchange_rate"]').value = orderData.header.exchange_rate;
        form.querySelector('input[name="from_location"]').value = orderData.header.from_location || '';
        form.querySelector('input[name="to_location"]').value = orderData.header.to_location || '';
        form.querySelector('textarea[name="notes"]').value = orderData.header.notes || '';

        // Remplir les articles
        const tbody = document.getElementById('orderItemsTableBody');
        tbody.innerHTML = '';
        itemCounter = 0;

        orderData.items.forEach(item => {
            itemCounter++;
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${itemCounter}</td>
                <td><input type="text" class="form-control form-control-sm" name="part_number_${itemCounter}" value="${item.part_number}" required></td>
                <td><input type="text" class="form-control form-control-sm" name="description_${itemCounter}" value="${item.description}" required></td>
                <td><input type="number" class="form-control form-control-sm" name="quantity_${itemCounter}" value="${item.quantity}" min="1" required onchange="calculateAmount(${itemCounter})"></td>
                <td><input type="number" class="form-control form-control-sm" name="u_fob_${itemCounter}" value="${item.u_fob}" step="0.01" required onchange="calculateAmount(${itemCounter})"></td>
                <td><span id="amount_${itemCounter}">${item.amount}</span></td>
                <td><button type="button" class="btn btn-sm btn-danger" onclick="removeOrderItem(this)"><i class="fas fa-trash"></i></button></td>
            `;
            tbody.appendChild(row);
        });

        // Marquer comme modification
        form.dataset.editId = orderData.header.id;
    }
</script>
{% endblock %}
