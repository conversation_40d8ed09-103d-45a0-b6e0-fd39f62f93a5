import sqlite3
from datetime import datetime
from io import BytesIO

import pandas as pd
from flask import Blueprint, request, jsonify, g, send_file

orders_bp = Blueprint('orders', __name__)

def get_db():
    """Obtenir une connexion à la base de données"""
    db = sqlite3.connect('logistics.db')
    db.row_factory = sqlite3.Row
    return db

@orders_bp.route('/orders', methods=['GET'])
def get_orders():
    """Récupérer toutes les commandes"""
    try:
        db = get_db()
        cursor = db.cursor()
        
        cursor.execute("""
            SELECT 
                oh.id, oh.order_number, oh.odoo_order_number, oh.order_date,
                oh.status, oh.currency, oh.exchange_rate, oh.from_location, oh.to_location,
                s.name as supplier_name,
                COUNT(oi.id) as items_count,
                SUM(oi.amount) as total_amount
            FROM order_headers oh
            LEFT JOIN suppliers s ON oh.supplier_id = s.id
            LEFT JOIN order_items oi ON oh.id = oi.order_header_id
            GROUP BY oh.id
            ORDER BY oh.created_at DESC
        """)
        
        orders = [dict(row) for row in cursor.fetchall()]
        db.close()
        
        return jsonify({'orders': orders})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@orders_bp.route('/orders/<int:order_id>', methods=['GET'])
def get_order(order_id):
    """Récupérer une commande spécifique avec ses détails"""
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Récupérer l'entête
        cursor.execute("""
            SELECT oh.*, s.name as supplier_name
            FROM order_headers oh
            LEFT JOIN suppliers s ON oh.supplier_id = s.id
            WHERE oh.id = ?
        """, (order_id,))
        
        header = cursor.fetchone()
        if not header:
            return jsonify({'error': 'Commande non trouvée'}), 404
        
        # Récupérer les articles
        cursor.execute("""
            SELECT * FROM order_items
            WHERE order_header_id = ?
            ORDER BY item_number
        """, (order_id,))
        
        items = [dict(row) for row in cursor.fetchall()]
        db.close()
        
        return jsonify({
            'header': dict(header),
            'items': items
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@orders_bp.route('/orders', methods=['POST'])
def create_order():
    """Créer une nouvelle commande"""
    data = request.get_json()
    
    # Validation
    if not data or 'header' not in data or 'items' not in data:
        return jsonify({'error': 'Données invalides'}), 400
    
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Insertion entête
        cursor.execute("""
            INSERT INTO order_headers (
                order_number, odoo_order_number, order_date, supplier_id,
                operation_type, description_goods, bank_name, lc_number,
                lc_validation, payment_term, price_term, currency, exchange_rate,
                from_location, to_location, status, notes, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            data['header']['order_number'],
            data['header'].get('odoo_order_number'),
            data['header']['order_date'],
            data['header']['supplier_id'],
            data['header']['operation_type'],
            data['header']['description_goods'],
            data['header'].get('bank_name'),
            data['header'].get('lc_number'),
            data['header'].get('lc_validation'),
            data['header'].get('payment_term'),
            data['header'].get('price_term'),
            data['header'].get('currency', 'USD'),
            data['header'].get('exchange_rate', 1.0),
            data['header']['from_location'],
            data['header']['to_location'],
            data['header'].get('status', 'DRAFT'),
            data['header'].get('notes'),
            g.user['id']
        ))
        
        order_id = cursor.lastrowid
        
        # Insertion des lignes
        for item in data['items']:
            cursor.execute("""
                INSERT INTO order_items (
                    order_header_id, item_number, part_number,
                    description, quantity, u_fob
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                order_id,
                item['item_number'],
                item['part_number'],
                item['description'],
                item['quantity'],
                item['u_fob']
            ))
        
        db.commit()
        db.close()
        return jsonify({'message': 'Commande créée', 'id': order_id}), 201
    
    except Exception as e:
        if db:
            db.rollback()
            db.close()
        return jsonify({'error': str(e)}), 500

@orders_bp.route('/orders/<int:order_id>', methods=['PUT'])
def update_order(order_id):
    """Mettre à jour une commande"""
    data = request.get_json()
    
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Mise à jour entête
        if 'header' in data:
            cursor.execute("""
                UPDATE order_headers SET
                    odoo_order_number = ?,
                    order_date = ?,
                    supplier_id = ?,
                    operation_type = ?,
                    description_goods = ?,
                    bank_name = ?,
                    lc_number = ?,
                    lc_validation = ?,
                    payment_term = ?,
                    price_term = ?,
                    currency = ?,
                    exchange_rate = ?,
                    from_location = ?,
                    to_location = ?,
                    status = ?,
                    notes = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                data['header'].get('odoo_order_number'),
                data['header'].get('order_date'),
                data['header'].get('supplier_id'),
                data['header'].get('operation_type'),
                data['header'].get('description_goods'),
                data['header'].get('bank_name'),
                data['header'].get('lc_number'),
                data['header'].get('lc_validation'),
                data['header'].get('payment_term'),
                data['header'].get('price_term'),
                data['header'].get('currency'),
                data['header'].get('exchange_rate'),
                data['header'].get('from_location'),
                data['header'].get('to_location'),
                data['header'].get('status'),
                data['header'].get('notes'),
                order_id
            ))
        
        # Mise à jour des lignes
        if 'items' in data:
            # Supprimer les lignes existantes
            cursor.execute("DELETE FROM order_items WHERE order_header_id = ?", (order_id,))
            
            # Insérer les nouvelles lignes
            for item in data['items']:
                cursor.execute("""
                    INSERT INTO order_items (
                        order_header_id, item_number, part_number,
                        description, quantity, u_fob
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    order_id,
                    item['item_number'],
                    item['part_number'],
                    item['description'],
                    item['quantity'],
                    item['u_fob']
                ))
        
        db.commit()
        db.close()
        return jsonify({'message': 'Commande mise à jour'})
    
    except Exception as e:
        if db:
            db.rollback()
            db.close()
        return jsonify({'error': str(e)}), 500

@orders_bp.route('/orders/<int:order_id>', methods=['DELETE'])
def delete_order(order_id):
    """Supprimer une commande"""
    try:
        db = get_db()
        cursor = db.cursor()
        
        cursor.execute("DELETE FROM order_headers WHERE id = ?", (order_id,))
        
        if cursor.rowcount == 0:
            return jsonify({'error': 'Commande non trouvée'}), 404
        
        db.commit()
        db.close()
        return jsonify({'message': 'Commande supprimée'})

    except Exception as e:
        if db:
            db.rollback()
            db.close()
        return jsonify({'error': str(e)}), 500

@orders_bp.route('/orders/<int:order_id>/export', methods=['GET'])
def export_order(order_id):
    """Exporter une commande au format Excel"""
    try:
        db = get_db()
        cursor = db.cursor()

        # Récupérer l'entête
        cursor.execute("""
            SELECT
                oh.order_number as numero_commande,
                oh.order_date,
                s.name as supplier_name,
                oh.operation_type,
                oh.description_goods,
                oh.currency,
                oh.exchange_rate,
                oh.from_location,
                oh.to_location
            FROM order_headers oh
            JOIN suppliers s ON oh.supplier_id = s.id
            WHERE oh.id = ?
        """, (order_id,))
        header = dict(cursor.fetchone())

        # Récupérer les lignes
        cursor.execute("""
            SELECT
                item_number as ITEM,
                ? as numero_commande,
                part_number as '#PART_NUMBER#',
                description as DESCRIPTION,
                quantity as Qty,
                u_fob as U_FOB,
                amount as AMOUNT
            FROM order_items
            WHERE order_header_id = ?
            ORDER BY item_number
        """, (header['numero_commande'], order_id))
        items = [dict(row) for row in cursor.fetchall()]

        # Création du DataFrame
        df = pd.DataFrame(items)

        # Création du fichier Excel
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # Feuille des lignes (format demandé)
            df.to_excel(writer, sheet_name='Items', index=False)

            # Feuille d'entête
            pd.DataFrame([header]).to_excel(writer, sheet_name='Header', index=False)

        output.seek(0)
        db.close()

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f"commande_{header['numero_commande']}.xlsx"
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@orders_bp.route('/orders/import', methods=['POST'])
def import_order():
    """Importer une commande depuis un fichier Excel"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Aucun fichier fourni'}), 400

        file = request.files['file']
        if not file.filename.endswith('.xlsx'):
            return jsonify({'error': 'Format non supporté'}), 400

        # Lire le fichier Excel
        xls = pd.ExcelFile(file)

        # Lire l'entête
        header_df = pd.read_excel(xls, sheet_name='Header')
        header_data = header_df.iloc[0].to_dict()

        # Lire les items
        items_df = pd.read_excel(xls, sheet_name='Items')
        items_data = items_df.to_dict('records')

        # Validation des données
        required_header_fields = ['numero_commande', 'order_date', 'supplier_name']
        if not all(field in header_data for field in required_header_fields):
            return jsonify({'error': 'Champs manquants dans l\'entête'}), 400

        required_item_fields = ['ITEM', '#PART_NUMBER#', 'DESCRIPTION', 'Qty', 'U_FOB']
        if not all(field in items_df.columns for field in required_item_fields):
            return jsonify({'error': 'Colonnes manquantes dans les items'}), 400

        # Trouver le fournisseur
        db = get_db()
        cursor = db.cursor()
        cursor.execute("SELECT id FROM suppliers WHERE name = ?", (header_data['supplier_name'],))
        supplier = cursor.fetchone()

        if not supplier:
            return jsonify({'error': 'Fournisseur non trouvé'}), 400

        # Créer la commande
        order_data = {
            'header': {
                'order_number': header_data['numero_commande'],
                'order_date': header_data['order_date'],
                'supplier_id': supplier['id'],
                'operation_type': header_data.get('operation_type', 'Goods for Resale as Is'),
                'description_goods': header_data.get('description_goods', 'OTHER'),
                'currency': header_data.get('currency', 'USD'),
                'exchange_rate': header_data.get('exchange_rate', 1.0),
                'from_location': header_data.get('from_location', ''),
                'to_location': header_data.get('to_location', ''),
                'status': 'DRAFT'
            },
            'items': [{
                'item_number': item['ITEM'],
                'part_number': item['#PART_NUMBER#'],
                'description': item['DESCRIPTION'],
                'quantity': item['Qty'],
                'u_fob': item['U_FOB']
            } for item in items_data]
        }

        db.close()

        # Utiliser la fonction de création existante
        return create_order_from_data(order_data)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@orders_bp.route('/orders/export-all', methods=['GET'])
def export_all_orders():
    """Exporter toutes les commandes au format Excel"""
    try:
        db = get_db()
        cursor = db.cursor()

        # Récupérer toutes les commandes avec leurs détails
        cursor.execute("""
            SELECT
                oh.order_number,
                oh.order_date,
                s.name as supplier_name,
                oh.operation_type,
                oh.description_goods,
                oh.currency,
                oh.exchange_rate,
                oh.from_location,
                oh.to_location,
                oh.status,
                oh.notes,
                COUNT(oi.id) as items_count,
                SUM(oi.amount) as total_amount
            FROM order_headers oh
            LEFT JOIN suppliers s ON oh.supplier_id = s.id
            LEFT JOIN order_items oi ON oh.id = oi.order_header_id
            GROUP BY oh.id
            ORDER BY oh.created_at DESC
        """)

        orders_data = [dict(row) for row in cursor.fetchall()]

        # Récupérer tous les articles
        cursor.execute("""
            SELECT
                oh.order_number,
                oi.item_number,
                oi.part_number,
                oi.description,
                oi.quantity,
                oi.u_fob,
                oi.amount,
                oi.received_quantity
            FROM order_items oi
            JOIN order_headers oh ON oi.order_header_id = oh.id
            ORDER BY oh.order_number, oi.item_number
        """)

        items_data = [dict(row) for row in cursor.fetchall()]
        db.close()

        # Créer le fichier Excel
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # Feuille des commandes
            pd.DataFrame(orders_data).to_excel(writer, sheet_name='Commandes', index=False)

            # Feuille des articles
            pd.DataFrame(items_data).to_excel(writer, sheet_name='Articles', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f"toutes_commandes_{datetime.now().strftime('%Y%m%d')}.xlsx"
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

def create_order_from_data(data):
    """Fonction helper pour créer une commande à partir de données"""
    try:
        db = get_db()
        cursor = db.cursor()

        # Insertion entête
        cursor.execute("""
            INSERT INTO order_headers (
                order_number, odoo_order_number, order_date, supplier_id,
                operation_type, description_goods, bank_name, lc_number,
                lc_validation, payment_term, price_term, currency, exchange_rate,
                from_location, to_location, status, notes, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            data['header']['order_number'],
            data['header'].get('odoo_order_number'),
            data['header']['order_date'],
            data['header']['supplier_id'],
            data['header']['operation_type'],
            data['header']['description_goods'],
            data['header'].get('bank_name'),
            data['header'].get('lc_number'),
            data['header'].get('lc_validation'),
            data['header'].get('payment_term'),
            data['header'].get('price_term'),
            data['header'].get('currency', 'USD'),
            data['header'].get('exchange_rate', 1.0),
            data['header']['from_location'],
            data['header']['to_location'],
            data['header'].get('status', 'DRAFT'),
            data['header'].get('notes'),
            g.user['id']
        ))

        order_id = cursor.lastrowid

        # Insertion des lignes
        for item in data['items']:
            cursor.execute("""
                INSERT INTO order_items (
                    order_header_id, item_number, part_number,
                    description, quantity, u_fob
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                order_id,
                item['item_number'],
                item['part_number'],
                item['description'],
                item['quantity'],
                item['u_fob']
            ))

        db.commit()
        db.close()
        return jsonify({'message': 'Commande importée', 'id': order_id}), 201

    except Exception as e:
        if db:
            db.rollback()
            db.close()
        return jsonify({'error': str(e)}), 500
