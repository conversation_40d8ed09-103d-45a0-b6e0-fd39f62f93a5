from flask import Blueprint, request, jsonify, g, send_file
import sqlite3
import pandas as pd
from io import BytesIO
from datetime import datetime

logistics_bp = Blueprint('logistics', __name__)

def get_db():
    """Obtenir une connexion à la base de données"""
    db = sqlite3.connect('logistics.db')
    db.row_factory = sqlite3.Row
    return db

@logistics_bp.route('/followups', methods=['GET'])
def get_followups():
    """Récupérer tous les suivis logistiques"""
    try:
        db = get_db()
        cursor = db.cursor()
        
        cursor.execute("""
            SELECT 
                lf.*,
                oh.order_number,
                s.name as supplier_name
            FROM logistic_followups lf
            JOIN order_headers oh ON lf.order_header_id = oh.id
            JOIN suppliers s ON oh.supplier_id = s.id
            ORDER BY lf.created_at DESC
        """)
        
        followups = [dict(row) for row in cursor.fetchall()]
        db.close()
        
        return jsonify({'followups': followups})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@logistics_bp.route('/followups', methods=['POST'])
def create_followup():
    """Créer un nouveau suivi logistique"""
    data = request.get_json()
    
    if not data or 'order_number' not in data:
        return jsonify({'error': 'Numéro de commande requis'}), 400
    
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Vérifier que la commande existe
        cursor.execute("SELECT id FROM order_headers WHERE order_number = ?", (data['order_number'],))
        order = cursor.fetchone()
        
        if not order:
            return jsonify({'error': 'Commande non trouvée'}), 404
        
        cursor.execute("""
            INSERT INTO logistic_followups (
                order_header_id, voyage_number, call_at_port,
                bill_of_lading, vessel_name, shipowner,
                actual_time_of_arrival, status, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            order['id'],
            data.get('voyage_number'),
            data.get('call_at_port'),
            data.get('bill_of_lading'),
            data.get('vessel_name'),
            data.get('shipowner'),
            data.get('actual_time_of_arrival'),
            data.get('status', 'en transit'),
            data.get('notes')
        ))
        
        followup_id = cursor.lastrowid
        db.commit()
        db.close()
        
        return jsonify({'message': 'Suivi créé', 'id': followup_id}), 201
    
    except Exception as e:
        if db:
            db.rollback()
            db.close()
        return jsonify({'error': str(e)}), 500

@logistics_bp.route('/followups/<int:followup_id>', methods=['PUT'])
def update_followup(followup_id):
    """Mettre à jour un suivi logistique"""
    data = request.get_json()
    
    try:
        db = get_db()
        cursor = db.cursor()
        
        cursor.execute("""
            UPDATE logistic_followups SET
                voyage_number = ?,
                call_at_port = ?,
                bill_of_lading = ?,
                vessel_name = ?,
                shipowner = ?,
                actual_time_of_arrival = ?,
                status = ?,
                notes = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (
            data.get('voyage_number'),
            data.get('call_at_port'),
            data.get('bill_of_lading'),
            data.get('vessel_name'),
            data.get('shipowner'),
            data.get('actual_time_of_arrival'),
            data.get('status'),
            data.get('notes'),
            followup_id
        ))
        
        if cursor.rowcount == 0:
            return jsonify({'error': 'Suivi non trouvé'}), 404
        
        db.commit()
        db.close()
        return jsonify({'message': 'Suivi mis à jour'})
    
    except Exception as e:
        if db:
            db.rollback()
            db.close()
        return jsonify({'error': str(e)}), 500

@logistics_bp.route('/costs', methods=['GET'])
def get_logistic_costs():
    """Récupérer tous les coûts logistiques"""
    try:
        db = get_db()
        cursor = db.cursor()
        
        cursor.execute("""
            SELECT 
                lc.*,
                oh.order_number,
                s.name as supplier_name
            FROM logistic_costs lc
            JOIN order_headers oh ON lc.order_header_id = oh.id
            JOIN suppliers s ON oh.supplier_id = s.id
            ORDER BY lc.created_at DESC
        """)
        
        costs = [dict(row) for row in cursor.fetchall()]
        db.close()
        
        return jsonify({'costs': costs})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@logistics_bp.route('/costs', methods=['POST'])
def create_logistic_cost():
    """Créer un nouveau coût logistique"""
    data = request.get_json()
    
    if not data or 'order_id' not in data:
        return jsonify({'error': 'ID de commande requis'}), 400
    
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Calcul des montants convertis
        exchange_rate = data.get('exchange_rate', 1.0)
        fob_amount = data.get('fob_amount', 0)
        freight = data.get('freight', 0)
        total_cif = fob_amount + freight
        
        fob_amount_dzd = fob_amount * exchange_rate
        freight_dzd = freight * exchange_rate
        total_cif_dzd = total_cif * exchange_rate
        
        # Insertion du coût principal
        cursor.execute("""
            INSERT INTO logistic_costs (
                order_header_id, exchange_rate, currency, shipment_type,
                odoo_order_number, odoo_order_date, from_location, to_location,
                operation_type, goods_description, bank_name, lc_number,
                lc_validation, payment_term, price_term, quantity_pcs,
                containers_20, containers_40, number_packages,
                goods_price_currency, fob_amount, freight, total_amount_cif,
                fob_amount_dzd, freight_dzd, total_amount_cif_dzd
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            data['order_id'],
            exchange_rate,
            data.get('currency', 'USD'),
            data.get('shipment_type', 'SEA'),
            data.get('odoo_order_number'),
            data.get('odoo_order_date'),
            data.get('from_location'),
            data.get('to_location'),
            data.get('operation_type'),
            data.get('goods_description'),
            data.get('bank_name'),
            data.get('lc_number'),
            data.get('lc_validation'),
            data.get('payment_term'),
            data.get('price_term'),
            data.get('quantity_pcs'),
            data.get('containers_20', 0),
            data.get('containers_40', 0),
            data.get('number_packages'),
            data.get('goods_price_currency'),
            fob_amount,
            freight,
            total_cif,
            fob_amount_dzd,
            freight_dzd,
            total_cif_dzd
        ))
        
        cost_id = cursor.lastrowid
        
        # Insertion des droits de douane
        if 'customs_duties' in data:
            for duty in data['customs_duties']:
                cursor.execute("""
                    INSERT INTO customs_duties (
                        logistic_cost_id, d3_number, d3_date,
                        quittance1_ttc, quittance1_tva, quittance1_ht, quittance2_ht
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    cost_id,
                    duty.get('d3_number'),
                    duty.get('d3_date'),
                    duty.get('quittance1_ttc'),
                    duty.get('quittance1_tva'),
                    duty.get('quittance1_ht'),
                    duty.get('quittance2_ht')
                ))
        
        # Insertion des frais portuaires
        if 'port_fees' in data:
            for fee in data['port_fees']:
                cursor.execute("""
                    INSERT INTO port_fees (
                        logistic_cost_id, fee_type, invoice_number,
                        invoice_date, total_ttc, tva, ht
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    cost_id,
                    fee.get('fee_type'),
                    fee.get('invoice_number'),
                    fee.get('invoice_date'),
                    fee.get('total_ttc'),
                    fee.get('tva'),
                    fee.get('ht')
                ))
        
        # Insertion des frais de compagnie maritime
        if 'shipping_fees' in data:
            for fee in data['shipping_fees']:
                cursor.execute("""
                    INSERT INTO shipping_company_fees (
                        logistic_cost_id, fee_type, invoice_number,
                        invoice_date, total_ttc, tva, ht
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    cost_id,
                    fee.get('fee_type'),
                    fee.get('invoice_number'),
                    fee.get('invoice_date'),
                    fee.get('total_ttc'),
                    fee.get('tva'),
                    fee.get('ht')
                ))
        
        # Insertion des autres dépenses
        if 'other_expenses' in data:
            for expense in data['other_expenses']:
                cursor.execute("""
                    INSERT INTO other_expenses (
                        logistic_cost_id, expense_type, invoice_number,
                        invoice_date, total_ttc, tva, ht
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    cost_id,
                    expense.get('expense_type'),
                    expense.get('invoice_number'),
                    expense.get('invoice_date'),
                    expense.get('total_ttc'),
                    expense.get('tva'),
                    expense.get('ht')
                ))
        
        db.commit()
        db.close()
        
        return jsonify({'message': 'Coût logistique créé', 'id': cost_id}), 201
    
    except Exception as e:
        if db:
            db.rollback()
            db.close()
        return jsonify({'error': str(e)}), 500
