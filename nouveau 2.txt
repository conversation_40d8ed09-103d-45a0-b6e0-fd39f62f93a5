Aide mois pour créer une application desktop avec SQLite et Python, avec authentification, la mission principale de cette application, aura à faire le suivie depuis la création des commandes, l'acheminement de la marchandise par voie maritime, aérienne ou divers, puis le suivi de l'arrivé au port de  la commande, puis le payement des frais engagés pour sa réception et en fin arriver à faire la réception de la marchandise avec attributions des couts logistique, donc le rôle de l'application est de: 1- enregistrements des commandes avec le détail de chaque commande, en utilisant ces champs model, avec  gestion en mode CRUD, et Importation et exportation de ces donnée, sous xlsx: (ITEM	numero_commande	#PART_NUMBER#	DESCRIPTION 	Qty	U_FOB	 AMOUNT 
1	AZEDFRTY2123	#8017029400#	HOSE-HEATER INLET	10	2,11	 21,10 
2	AZEDFRTY2123	#5022072200#	FRT WINDSHIELD ASSY,	8	99,13	 793,04 
3	AZEDFRTY2123	#4114870644#	Oil filling valve of transmission	10	5,12	 51,20 ),  Aussi, 2 - Le suivie logistique de chaque commande, à travers les données de cette table et voici les champs nécessaire à enregistrer et gérer en mode CRUD avec mode import et export sous xlsx  : ( Voyage Number - "CALL AT PORT" (ESCALE) N°: ;
	Bill of Lading (B/L) (Conaissement) N°:;
	Vessel Name (Navire):;
	Shipowner (Armateur) : ;
	Actual Time of Arrival(Date Accostage)  ; numero_commande : ) Aussi , 3- Enregistrement des couts Logistiques de chaque commande , dans une base de donnée, où les données afferentes seront geées ; à savoir : ( General Information: 
	Exchange Rate Used : DZD/(CURRENCY:(USD / EURO / YUAN )) : (FORMAT MONAITRE FRANçAIS A CINQ CHIFFRE APRES LA VIRGULE) 
	SUPPLIER NAME: 
	Shipment Type * (LISTE DEROULANTE : SEA, AIR , Express , Other )
	ORDER NUMBER * (odoo):  (FORMA TEXTE )
	DATE OF ORDER NUMBER * :  
	numero_commande :
	numero_commande date :
	FROM: 
	TO : 
	OPERATION TYPE: (LISTE DEROULANTE : Operational Expenses (OpEx) / Investment (or Equipment) / Reinvestment / Raw Materials and Semi-finished Products / Spare Parts (for maintenance or specific resale) / Goods for Resale as Is / Services (intangible) / Temporary Importation (or Temporary Admission)), 
	DESCRIPTION OF THE GOODS:(LISTE DEROULANTE :  SPARE PARTS FOR VEHICLES/LUBRICANT/ ACCESSORY/OTHER)
	Bank Name	:
	L/C  N#	:
	Validation L.C	:
	PAYMENT TERM:
	PRICE TERM:
	QUANTITY (PCS) :
	Number CONTAINERS 20 Pieds: 
	Number CONTAINERS 40 Pieds:
	Number PACKAGES :
Goods Price in Currency(CURRENCY:(LISTE DEROULANTE : USD / EURO / YUAN ): 
		FOB AMOUNT:
		FREIGHT:
		TOTAL AMOUNT CIF (FOB + Freight)
Conversion to DZD :
		FOB AMOUNT DZD(Automatic conversion):
		FREIGHT DZD (Automatic conversion):
		TOTAL AMOUNT CIF DZD (FOB + Freight) (Automatic conversion):
Customs Duties:
		COST ALLOCATION NAME(ALGERIA CUSTOMS):
		D3 N#	:
		D3 Date	:
		QUITTANCE 1:
			Customs Duties1  DZD Total TTC :
			Customs Duties1   DZD TVA :
			Customs Duties1   DZD HT (TTC - TVA):
		QUITTANCE 2:
			Customs Duties2   DZD HT (TTC - TVA):
	OVERALL TOTALS OF CUSTOMS DUTIES:
		Customs Duties "Overall Totals" DZD Total TTC : = "Customs Duties1  DZD Total TTC" + "Customs Duties2  DZD Total TTC"
		Customs Duties  "Overall Totals"  DZD TVA := " Customs Duties1   DZD TVA " + "Customs Duties2   DZD TVA "
		Customs Duties  "Overall Totals"  DZD HT (TTC - TVA):= " Customs Duties1   DZD HT (TTC - TVA)" + "Customs Duties2   DZD HT (TTC - TVA)"
PORT FEES :	
		COST ALLOCATION NAME (IMPORT DELIVERY):
			IMPORT DELIVERY INVOICE N#:
			IMPORT DELIVERY INVOICE DATE:
			IMPORT DELIVERY Total TTC :
			IMPORT DELIVERY TVA:
			IMPORT DELIVERY HT (TTC - TVA):
		COST ALLOCATION NAME (CUSTOMS INSPECTION):
			CUSTOMS INSPECTION INVOICE N#:
			CUSTOMS INSPECTION INVOICE DATE:
			CUSTOMS INSPECTION Total TTC :
			CUSTOMS INSPECTION TVA:
			CUSTOMS INSPECTION HT (TTC - TVA):
	OVERALL TOTALS OF PORT FEES :
			PORT FEES "Overall Totals" DZD Total TTC : "IMPORT DELIVERY Total TTC " + "CUSTOMS INSPECTION Total TTC"  + "DEMURRAGE Total TTC (DZD)" .
			PORT FEES "Overall Totals"  DZD TVA :=  = "IMPORT DELIVERY  TVA (DZD) " + "CUSTOMS INSPECTION  TVA (DZD)" + "DEMURRAGE Total TTC (DZD)" .
			PORT FEES "Overall Totals"  DZD HT (TTC - TVA):= "IMPORT DELIVERY HT (DZD) (TTC - TVA) " + "CUSTOMS INSPECTION HT (DZD) (TTC - TVA)" + "DEMURRAGE HT (DZD) (TTC - TVA)" .
SHIPPING COMPANY FEES
		COST ALLOCATION NAME (SHIPPING AGENCY SERVICES):
			SHIPPING AGENCY SERVICES INVOICE N#:
			SHIPPING AGENCY SERVICES INVOICE DATE:
			SHIPPING AGENCY SERVICES Total TTC (DZD) * :
			SHIPPING AGENCY SERVICES TVA (DZD) *:
			SHIPPING AGENCY SERVICES HT (DZD) (TTC - TVA):
		COST ALLOCATION NAME (EMPTY CONTAINERS):
			EMPTY CONTAINERS RETURN INVOICE N#:
			EMPTY CONTAINERS RETURN INVOICE DATE:
			EMPTY CONTAINERS RETURN Total TTC (DZD)
			EMPTY CONTAINERS RETURN TVA (DZD)
			EMPTY CONTAINERS RETURN HT (DZD) (TTC - TVA)
		COST ALLOCATION NAME (DEMURRAGE IF PRESENT):
			DEMURRAGE INVOICE N#:
			DEMURRAGE INVOICE DATE:		
			DEMURRAGE HT (DZD) (This currency field must be entered manually.)
	OVERALL TOTALS OF SHIPPING COMPANY FEES :
			SHIPPING COMPANY FEES "Overall Totals" DZD Total TTC      : = "TERMINAL HANDLING-OPERATIONS Total TTC (DZD) * :"     + "EMPTY CONTAINERS RETURN Total TTC (DZD)"
			SHIPPING COMPANY FEES "Overall Totals"  DZD TVA           : = "TERMINAL HANDLING-OPERATIONS TVA (DZD) *:"           +  "EMPTY CONTAINERS RETURN TVA (DZD)"
			SHIPPING COMPANY FEES "Overall Totals"  DZD HT (TTC - TVA): = "TERMINAL HANDLING-OPERATIONS HT (DZD) (TTC - TVA):"	+  "EMPTY CONTAINERS RETURN HT (DZD) (TTC - TVA)" +  "DEMURRAGE HT (DZD) (This currency field must be entered manually.)"						
OTHER MISCELLANEOUS EXPENSES		
		COST ALLOCATION NAME (OTHER MISCELLANEOUS EXPENSES):
			OTHER MISCELLANEOUS EXPENSES INVOICE N#:
			OTHER MISCELLANEOUS EXPENSES INVOICE DATE:
			OTHER MISCELLANEOUS EXPENSES TTC (DZD) * :
			OTHER MISCELLANEOUS EXPENSES TVA (DZD) *:
			OTHER MISCELLANEOUS EXPENSES HT (DZD) (TTC - TVA):				
TRANSIT SERVICES EXPENSES		
		COST ALLOCATION NAME (TRANSIT SERVICES EXPENSES):
			TRANSIT SERVICES EXPENSES INVOICE N#:
			TRANSIT SERVICES EXPENSES INVOICE DATE:
			TRANSIT SERVICES EXPENSES TTC (DZD) * :
			TRANSIT SERVICES EXPENSES TVA (DZD) *:
			TRANSIT SERVICES EXPENSES HT (DZD) (TTC - TVA):				
Landed Cost HT: ="TRANSIT SERVICES EXPENSES HT (DZD) (TTC - TVA):	"+ "OTHER MISCELLANEOUS EXPENSES HT (DZD) (TTC - TVA):	" + "SHIPPING COMPANY FEES "Overall Totals"  DZD HT (TTC - TVA): " +" PORT FEES "Overall Totals"  DZD HT (TTC - TVA):" + " PORT FEES "Overall Totals"  DZD HT (TTC - TVA): " + "TOTAL AMOUNT CIF DZD (FOB + Freight) (Automatic conversion): " ;
Landed cost coefficient	:
Total Paid	TTC : ) ; 4-dans une autre table, on fait la réception des articles en commande, en leur attribuant les couts nécessaire, pour avoir chaque item avec son cout de revient unitaire; et en fin 5- faire les analyses et kpi et rapports et ce d'une manière automatique