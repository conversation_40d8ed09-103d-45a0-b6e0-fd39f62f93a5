from flask import Blueprint, request, jsonify, g
import sqlite3
from datetime import datetime

receipts_bp = Blueprint('receipts', __name__)

def get_db():
    """Obtenir une connexion à la base de données"""
    db = sqlite3.connect('logistics.db')
    db.row_factory = sqlite3.Row
    return db

@receipts_bp.route('/receipts/prepare/<order_number>', methods=['GET'])
def prepare_receipt(order_number):
    """Préparer les données pour une réception"""
    try:
        db = get_db()
        cursor = db.cursor()
        
        # 1. Récupérer la commande
        cursor.execute("""
            SELECT oh.id as order_id, oh.order_number, oh.currency, oh.exchange_rate,
                   s.name as supplier_name, oh.status
            FROM order_headers oh
            JOIN suppliers s ON oh.supplier_id = s.id
            WHERE oh.order_number = ?
        """, (order_number,))
        order = cursor.fetchone()
        
        if not order:
            return jsonify({'error': 'Commande non trouvée'}), 404
        
        # 2. Récupérer les articles de la commande avec quantités restantes
        cursor.execute("""
            SELECT oi.id, oi.item_number, oi.part_number, oi.description, 
                   oi.quantity, oi.u_fob, oi.amount,
                   oi.quantity - COALESCE(
                       (SELECT SUM(ri.quantity_received) 
                        FROM receipt_items ri 
                        JOIN receipts r ON ri.receipt_id = r.id
                        WHERE ri.order_item_id = oi.id), 0
                   ) as quantity_pending
            FROM order_items oi
            WHERE oi.order_header_id = ?
            HAVING quantity_pending > 0
        """, (order['order_id'],))
        items = [dict(row) for row in cursor.fetchall()]
        
        # 3. Récupérer les coûts logistiques associés
        cursor.execute("""
            SELECT lc.id, lc.total_amount_cif_dzd, lc.freight_dzd,
                   COALESCE((SELECT SUM(cd.total_ttc) FROM customs_duties cd WHERE cd.logistic_cost_id = lc.id), 0) as total_customs,
                   COALESCE((SELECT SUM(pf.total_ttc) FROM port_fees pf WHERE pf.logistic_cost_id = lc.id), 0) as total_port_fees,
                   COALESCE((SELECT SUM(scf.total_ttc) FROM shipping_company_fees scf WHERE scf.logistic_cost_id = lc.id), 0) as total_shipping_fees,
                   COALESCE((SELECT SUM(oe.total_ttc) FROM other_expenses oe WHERE oe.logistic_cost_id = lc.id), 0) as total_other_expenses
            FROM logistic_costs lc
            WHERE lc.order_header_id = ?
        """, (order['order_id'],))
        logistic_cost = cursor.fetchone()
        
        if not logistic_cost:
            return jsonify({'error': 'Aucun coût logistique trouvé pour cette commande'}), 400
        
        # Calculer le total des coûts logistiques
        total_logistic_cost = (
            (logistic_cost['total_customs'] or 0) +
            (logistic_cost['total_port_fees'] or 0) +
            (logistic_cost['total_shipping_fees'] or 0) +
            (logistic_cost['total_other_expenses'] or 0)
        )
        
        # Calculer la valeur totale des marchandises en DZD
        cursor.execute("""
            SELECT SUM(oi.amount * oh.exchange_rate) as total_goods_value
            FROM order_items oi
            JOIN order_headers oh ON oi.order_header_id = oh.id
            WHERE oh.id = ?
        """, (order['order_id'],))
        total_goods_value = cursor.fetchone()['total_goods_value'] or 0
        
        db.close()
        
        return jsonify({
            'order': dict(order),
            'items': items,
            'logistic_cost': {
                'id': logistic_cost['id'],
                'total_customs': logistic_cost['total_customs'],
                'total_port_fees': logistic_cost['total_port_fees'],
                'total_shipping_fees': logistic_cost['total_shipping_fees'],
                'total_other_expenses': logistic_cost['total_other_expenses'],
                'total_logistic_cost': total_logistic_cost
            },
            'total_goods_value_dzd': total_goods_value,
            'exchange_rate': order['exchange_rate']
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@receipts_bp.route('/receipts', methods=['POST'])
def create_receipt():
    """Créer une réception avec calcul des coûts de revient"""
    data = request.get_json()
    
    # Validation de base
    if not data or 'order_id' not in data or 'items' not in data:
        return jsonify({'error': 'Données invalides'}), 400
    
    try:
        db = get_db()
        cursor = db.cursor()
        
        # 1. Vérifier que la commande existe
        cursor.execute("""
            SELECT id, order_number, exchange_rate 
            FROM order_headers 
            WHERE id = ? AND status != 'CANCELLED'
        """, (data['order_id'],))
        order = cursor.fetchone()
        
        if not order:
            return jsonify({'error': 'Commande non trouvée ou annulée'}), 404
        
        # 2. Vérifier les coûts logistiques
        cursor.execute("""
            SELECT id FROM logistic_costs WHERE order_header_id = ?
        """, (data['order_id'],))
        logistic_cost = cursor.fetchone()
        
        if not logistic_cost:
            return jsonify({'error': 'Aucun coût logistique trouvé pour cette commande'}), 400
        
        # 3. Calculer la valeur totale des articles reçus
        total_items_value = 0
        valid_items = []
        
        for item in data['items']:
            cursor.execute("""
                SELECT id, quantity, u_fob, amount, 
                       quantity - COALESCE(
                           (SELECT SUM(quantity_received) 
                            FROM receipt_items ri
                            JOIN receipts r ON ri.receipt_id = r.id
                            WHERE ri.order_item_id = order_items.id), 0
                       ) as quantity_remaining
                FROM order_items
                WHERE id = ? AND order_header_id = ?
            """, (item['order_item_id'], data['order_id']))
            db_item = cursor.fetchone()
            
            if not db_item:
                continue
            
            if item['quantity_received'] > db_item['quantity_remaining']:
                return jsonify({
                    'error': f"Quantité reçue supérieure à la quantité restante pour l'article {item['order_item_id']}"
                }), 400
            
            item_value = item['quantity_received'] * db_item['u_fob'] * order['exchange_rate']
            total_items_value += item_value
            valid_items.append({
                **item,
                'unit_price_dzd': db_item['u_fob'] * order['exchange_rate'],
                'item_value': item_value
            })
        
        if total_items_value <= 0:
            return jsonify({'error': 'Aucune valeur marchande valide'}), 400
        
        # 4. Récupérer le total des coûts logistiques
        cursor.execute("""
            SELECT 
                COALESCE((SELECT SUM(total_ttc) FROM customs_duties WHERE logistic_cost_id = ?), 0) as total_customs,
                COALESCE((SELECT SUM(total_ttc) FROM port_fees WHERE logistic_cost_id = ?), 0) as total_port_fees,
                COALESCE((SELECT SUM(total_ttc) FROM shipping_company_fees WHERE logistic_cost_id = ?), 0) as total_shipping_fees,
                COALESCE((SELECT SUM(total_ttc) FROM other_expenses WHERE logistic_cost_id = ?), 0) as total_other_expenses
        """, (logistic_cost['id'], logistic_cost['id'], logistic_cost['id'], logistic_cost['id']))
        costs = cursor.fetchone()
        total_logistic_cost = costs['total_customs'] + costs['total_port_fees'] + costs['total_shipping_fees'] + costs['total_other_expenses']
        
        # 5. Créer la réception
        cursor.execute("""
            INSERT INTO receipts (
                order_header_id, receipt_date, logistic_cost_id,
                customs_clearance_date, warehouse_location,
                total_logistic_cost_dzd, total_goods_value_dzd,
                notes, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            data['order_id'],
            data['receipt_date'],
            logistic_cost['id'],
            data.get('customs_clearance_date'),
            data.get('warehouse_location'),
            total_logistic_cost,
            total_items_value,
            data.get('notes'),
            g.user['id']
        ))
        receipt_id = cursor.lastrowid
        
        # 6. Ajouter les articles reçus avec calcul des coûts
        unit_costs = []
        for item in valid_items:
            # Calcul de la part des coûts logistiques proportionnelle à la valeur
            cost_share = (item['item_value'] / total_items_value) * total_logistic_cost
            
            # Répartition des coûts (proportionnelle aux totaux)
            total_all_costs = costs['total_customs'] + costs['total_port_fees'] + costs['total_shipping_fees'] + costs['total_other_expenses']
            if total_all_costs > 0:
                customs_share = cost_share * (costs['total_customs'] / total_all_costs)
                logistic_share = cost_share * ((costs['total_port_fees'] + costs['total_shipping_fees']) / total_all_costs)
                other_share = cost_share * (costs['total_other_expenses'] / total_all_costs)
            else:
                customs_share = logistic_share = other_share = 0
            
            cursor.execute("""
                INSERT INTO receipt_items (
                    receipt_id, order_item_id, quantity_received,
                    unit_price_dzd, logistic_cost_share_dzd,
                    customs_duties_share_dzd, other_costs_share_dzd
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                receipt_id,
                item['order_item_id'],
                item['quantity_received'],
                item['unit_price_dzd'],
                logistic_share,
                customs_share,
                other_share
            ))
            
            unit_landed_cost = item['unit_price_dzd'] + (cost_share / item['quantity_received'])
            unit_costs.append({
                'order_item_id': item['order_item_id'],
                'unit_landed_cost': unit_landed_cost
            })
        
        # 7. Mettre à jour le statut de la commande si complètement reçue
        cursor.execute("""
            SELECT 
                SUM(oi.quantity) as total_ordered,
                COALESCE(SUM(ri_totals.total_received), 0) as total_received
            FROM order_items oi
            LEFT JOIN (
                SELECT ri.order_item_id, SUM(ri.quantity_received) as total_received
                FROM receipt_items ri
                GROUP BY ri.order_item_id
            ) ri_totals ON oi.id = ri_totals.order_item_id
            WHERE oi.order_header_id = ?
        """, (data['order_id'],))
        totals = cursor.fetchone()
        
        if totals['total_received'] >= totals['total_ordered']:
            cursor.execute("""
                UPDATE order_headers 
                SET status = 'DELIVERED', updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (data['order_id'],))
        
        db.commit()
        db.close()
        
        # 8. Retourner les détails avec les coûts calculés
        return jsonify({
            'message': 'Réception enregistrée',
            'receipt_id': receipt_id,
            'total_landed_cost': total_items_value + total_logistic_cost,
            'unit_costs': unit_costs
        }), 201
    
    except Exception as e:
        if db:
            db.rollback()
            db.close()
        return jsonify({'error': str(e)}), 500

@receipts_bp.route('/receipts/<int:receipt_id>/costs', methods=['GET'])
def get_receipt_costs(receipt_id):
    """Récupérer les coûts détaillés d'une réception"""
    try:
        db = get_db()
        cursor = db.cursor()

        # 1. Récupérer l'entête de réception
        cursor.execute("""
            SELECT r.*, oh.order_number, lc.total_amount_cif_dzd
            FROM receipts r
            JOIN order_headers oh ON r.order_header_id = oh.id
            JOIN logistic_costs lc ON r.logistic_cost_id = lc.id
            WHERE r.id = ?
        """, (receipt_id,))
        receipt = cursor.fetchone()

        if not receipt:
            return jsonify({'error': 'Réception non trouvée'}), 404

        # 2. Récupérer les articles avec coûts détaillés
        cursor.execute("""
            SELECT
                ri.*,
                oi.item_number, oi.part_number, oi.description,
                oi.quantity as ordered_quantity,
                oi.u_fob, oi.amount
            FROM receipt_items ri
            JOIN order_items oi ON ri.order_item_id = oi.id
            WHERE ri.receipt_id = ?
        """, (receipt_id,))
        items = [dict(row) for row in cursor.fetchall()]

        # 3. Calculer les totaux
        total_customs = sum(item['customs_duties_share_dzd'] or 0 for item in items)
        total_logistic = sum(item['logistic_cost_share_dzd'] or 0 for item in items)
        total_other = sum(item['other_costs_share_dzd'] or 0 for item in items)

        db.close()

        return jsonify({
            'receipt': dict(receipt),
            'items': items,
            'cost_breakdown': {
                'total_goods_value': receipt['total_goods_value_dzd'],
                'total_customs_duties': total_customs,
                'total_logistic_costs': total_logistic,
                'total_other_costs': total_other,
                'total_landed_cost': receipt['total_landed_cost_dzd'],
                'landed_cost_coefficient': (
                    receipt['total_landed_cost_dzd'] / receipt['total_goods_value_dzd']
                    if receipt['total_goods_value_dzd'] > 0 else 0
                )
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@receipts_bp.route('/items/<int:order_item_id>/unit-cost', methods=['GET'])
def get_item_unit_cost(order_item_id):
    """Récupérer le coût unitaire d'un article"""
    try:
        db = get_db()
        cursor = db.cursor()

        # 1. Récupérer toutes les réceptions pour cet article
        cursor.execute("""
            SELECT
                ri.quantity_received,
                ri.unit_landed_cost_dzd,
                ri.total_line_cost_dzd,
                r.receipt_date,
                oh.order_number
            FROM receipt_items ri
            JOIN receipts r ON ri.receipt_id = r.id
            JOIN order_headers oh ON r.order_header_id = oh.id
            WHERE ri.order_item_id = ?
            ORDER BY r.receipt_date
        """, (order_item_id,))
        receipts = [dict(row) for row in cursor.fetchall()]

        if not receipts:
            return jsonify({'error': 'Article non reçu'}), 404

        # 2. Calculer la moyenne pondérée
        total_quantity = sum(r['quantity_received'] for r in receipts)
        total_value = sum(r['total_line_cost_dzd'] for r in receipts)
        weighted_avg = total_value / total_quantity if total_quantity > 0 else 0

        # 3. Récupérer les infos de l'article
        cursor.execute("""
            SELECT oi.*, oh.currency, oh.exchange_rate
            FROM order_items oi
            JOIN order_headers oh ON oi.order_header_id = oh.id
            WHERE oi.id = ?
        """, (order_item_id,))
        item = cursor.fetchone()

        db.close()

        return jsonify({
            'order_item': dict(item),
            'receipts': receipts,
            'weighted_average_unit_cost': weighted_avg,
            'total_quantity_received': total_quantity,
            'total_value': total_value,
            'fob_unit_price_dzd': item['u_fob'] * item['exchange_rate']
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@receipts_bp.route('/receipts', methods=['GET'])
def get_receipts():
    """Récupérer toutes les réceptions"""
    try:
        db = get_db()
        cursor = db.cursor()

        cursor.execute("""
            SELECT
                r.*,
                oh.order_number,
                s.name as supplier_name,
                COUNT(ri.id) as items_count
            FROM receipts r
            JOIN order_headers oh ON r.order_header_id = oh.id
            JOIN suppliers s ON oh.supplier_id = s.id
            LEFT JOIN receipt_items ri ON r.id = ri.receipt_id
            GROUP BY r.id
            ORDER BY r.created_at DESC
        """)

        receipts = [dict(row) for row in cursor.fetchall()]
        db.close()

        return jsonify({'receipts': receipts})

    except Exception as e:
        return jsonify({'error': str(e)}), 500
